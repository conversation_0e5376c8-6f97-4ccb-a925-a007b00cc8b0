<?php
/**
 * Database check script to verify tables and data
 */

require_once 'config/config.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Check - Flori Construction</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>🔍 Database Check Results</h1>

    <?php
    try {
        echo "<div class='section'>";
        echo "<h2>📊 Database Connection</h2>";
        
        // Test basic connection
        $testQuery = $db->fetchOne("SELECT NOW() as NOW()");
        echo "<p class='success'>✅ Database connection successful</p>";
        echo "<p>Current time: " . $testQuery['NOW()'] . "</p>";
        
        echo "</div>";

        // Check required tables
        echo "<div class='section'>";
        echo "<h2>📋 Required Tables</h2>";
        
        $requiredTables = ['users', 'projects', 'api_tokens', 'media', 'content', 'testimonials'];
        
        foreach ($requiredTables as $table) {
            try {
                $result = $db->query("SHOW TABLES LIKE '$table'");
                if ($result->rowCount() > 0) {
                    echo "<p class='success'>✅ Table '$table' exists</p>";
                } else {
                    echo "<p class='error'>❌ Table '$table' missing</p>";
                }
            } catch (Exception $e) {
                echo "<p class='error'>❌ Error checking table '$table': " . $e->getMessage() . "</p>";
            }
        }
        
        echo "</div>";

        // Check users table
        echo "<div class='section'>";
        echo "<h2>👥 Users Table</h2>";
        
        try {
            $userCount = $db->fetchOne("SELECT COUNT(*) as count FROM users")['count'];
            echo "<p>Total users: $userCount</p>";
            
            if ($userCount > 0) {
                $users = $db->fetchAll("SELECT id, username, email, role, is_active, created_at FROM users LIMIT 5");
                echo "<table>";
                echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Role</th><th>Active</th><th>Created</th></tr>";
                foreach ($users as $user) {
                    $activeStatus = $user['is_active'] ? 'Yes' : 'No';
                    echo "<tr>";
                    echo "<td>{$user['id']}</td>";
                    echo "<td>{$user['username']}</td>";
                    echo "<td>{$user['email']}</td>";
                    echo "<td>{$user['role']}</td>";
                    echo "<td>$activeStatus</td>";
                    echo "<td>{$user['created_at']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='warning'>⚠️ No users found</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error checking users: " . $e->getMessage() . "</p>";
        }
        
        echo "</div>";

        // Check projects table
        echo "<div class='section'>";
        echo "<h2>🏗️ Projects Table</h2>";
        
        try {
            $projectCount = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_active = 1")['count'];
            echo "<p>Active projects: $projectCount</p>";
            
            if ($projectCount > 0) {
                $projects = $db->fetchAll("SELECT id, title, project_type, location, is_featured, created_at FROM projects WHERE is_active = 1 LIMIT 5");
                echo "<table>";
                echo "<tr><th>ID</th><th>Title</th><th>Type</th><th>Location</th><th>Featured</th><th>Created</th></tr>";
                foreach ($projects as $project) {
                    $featuredStatus = $project['is_featured'] ? 'Yes' : 'No';
                    echo "<tr>";
                    echo "<td>{$project['id']}</td>";
                    echo "<td>" . htmlspecialchars($project['title']) . "</td>";
                    echo "<td>{$project['project_type']}</td>";
                    echo "<td>" . htmlspecialchars($project['location'] ?? 'N/A') . "</td>";
                    echo "<td>$featuredStatus</td>";
                    echo "<td>{$project['created_at']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='warning'>⚠️ No active projects found</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error checking projects: " . $e->getMessage() . "</p>";
        }
        
        echo "</div>";

        // Check API tokens
        echo "<div class='section'>";
        echo "<h2>🔑 API Tokens</h2>";
        
        try {
            $tokenCount = $db->fetchOne("SELECT COUNT(*) as count FROM api_tokens WHERE is_active = 1")['count'];
            echo "<p>Active tokens: $tokenCount</p>";
            
            $expiredCount = $db->fetchOne("SELECT COUNT(*) as count FROM api_tokens WHERE is_active = 1 AND expires_at < NOW()")['count'];
            echo "<p>Expired tokens: $expiredCount</p>";
            
            if ($tokenCount > 0) {
                $tokens = $db->fetchAll("
                    SELECT t.id, u.username, t.expires_at, t.is_active, t.created_at 
                    FROM api_tokens t 
                    JOIN users u ON t.user_id = u.id 
                    WHERE t.is_active = 1 
                    ORDER BY t.created_at DESC 
                    LIMIT 5
                ");
                echo "<table>";
                echo "<tr><th>ID</th><th>User</th><th>Expires</th><th>Active</th><th>Created</th></tr>";
                foreach ($tokens as $token) {
                    $isExpired = strtotime($token['expires_at']) < time();
                    $expiresClass = $isExpired ? 'error' : 'success';
                    echo "<tr>";
                    echo "<td>{$token['id']}</td>";
                    echo "<td>{$token['username']}</td>";
                    echo "<td class='$expiresClass'>{$token['expires_at']}</td>";
                    echo "<td>{$token['is_active']}</td>";
                    echo "<td>{$token['created_at']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error checking API tokens: " . $e->getMessage() . "</p>";
        }
        
        echo "</div>";

        // Test API endpoints
        echo "<div class='section'>";
        echo "<h2>🔗 API Endpoint Tests</h2>";
        
        // Test ping endpoint
        try {
            $pingUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/api/mobile.php?action=ping";
            $pingResponse = file_get_contents($pingUrl);
            $pingData = json_decode($pingResponse, true);
            
            if ($pingData && $pingData['success']) {
                echo "<p class='success'>✅ Ping endpoint working</p>";
            } else {
                echo "<p class='error'>❌ Ping endpoint failed</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Ping test failed: " . $e->getMessage() . "</p>";
        }
        
        echo "</div>";

    } catch (Exception $e) {
        echo "<div class='section'>";
        echo "<p class='error'>❌ Critical error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>

    <div class='section'>
        <h2>🔧 Quick Actions</h2>
        <p><a href="mobile-app/debug-projects.html">🔍 Debug Projects Tool</a></p>
        <p><a href="mobile-app/index.html">📱 Mobile App</a></p>
        <p><a href="test-projects-api.php">🧪 Test Projects API</a></p>
    </div>

</body>
</html>
