<?php
/**
 * Simple Error Test
 * This will help us isolate exactly where the error is coming from
 */

echo "<h1>Simple Error Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; margin: 10px 0; border-left: 4px solid green; }
    .error { color: red; background: #fff0f0; padding: 10px; margin: 10px 0; border-left: 4px solid red; }
    .warning { color: orange; background: #fff8f0; padding: 10px; margin: 10px 0; border-left: 4px solid orange; }
    .info { color: blue; background: #f0f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid blue; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
</style>";

// Clear any existing sessions
session_start();
session_destroy();
session_start();

echo "<h2>Test 1: Direct Database Connection</h2>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=flori_construction;charset=utf8mb4", "root", "", [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "<div class='success'>✅ Direct connection works</div>";
    
    $result = $pdo->query("SELECT NOW() as current_time")->fetch();
    echo "<div class='success'>✅ NOW() query works: " . $result['current_time'] . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Direct connection failed: " . $e->getMessage() . "</div>";
    exit;
}

echo "<h2>Test 2: Database Class Only</h2>";
try {
    require_once 'config/database.php';
    echo "<div class='success'>✅ Database class loaded</div>";
    
    $testDb = new Database();
    echo "<div class='success'>✅ Database class instantiated</div>";
    
    $result = $testDb->fetchOne("SELECT NOW() as current_time");
    echo "<div class='success'>✅ Database class query works: " . $result['current_time'] . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database class failed: " . $e->getMessage() . "</div>";
    echo "<div class='error'>This is where the error is occurring!</div>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    exit;
}

echo "<h2>Test 3: Full Config Loading</h2>";
try {
    // Clear any previous includes
    $included_files = get_included_files();
    echo "<div class='info'>Files already included: " . count($included_files) . "</div>";
    
    // Load config
    require_once 'config/config.php';
    echo "<div class='success'>✅ Config loaded</div>";
    
    if (isset($db)) {
        echo "<div class='success'>✅ Global \$db created</div>";
        
        $result = $db->fetchOne("SELECT NOW() as current_time");
        echo "<div class='success'>✅ Global \$db query works: " . $result['current_time'] . "</div>";
    } else {
        echo "<div class='error'>❌ Global \$db not created</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Config loading failed: " . $e->getMessage() . "</div>";
    echo "<div class='error'>This is where the error is occurring!</div>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<h2>Test 4: Check for Problematic Queries</h2>";

// Test various problematic queries to see which one fails
$testQueries = [
    "SELECT current_time",           // This should fail
    "SELECT current_date",           // This should fail  
    "SELECT current_timestamp",      // This might work
    "SELECT NOW() as current_time",  // This should work
    "SELECT CURRENT_TIMESTAMP",      // This should work
];

foreach ($testQueries as $query) {
    try {
        echo "<div class='info'>Testing: <code>" . htmlspecialchars($query) . "</code></div>";
        $result = $pdo->query($query)->fetch();
        echo "<div class='success'>✅ Query worked</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Query failed: " . $e->getMessage() . "</div>";
    }
}

echo "<h2>Test 5: Check for Auto-executing Code</h2>";

// Check if any files are executing queries automatically
$autoExecuteFiles = [
    'index.php',
    'admin/index.php'
];

foreach ($autoExecuteFiles as $file) {
    if (file_exists($file)) {
        echo "<div class='info'>Checking: $file</div>";
        $content = file_get_contents($file);
        
        // Look for database queries in the file
        if (preg_match('/\$db->(?:fetchOne|fetchAll|query)/i', $content)) {
            echo "<div class='warning'>⚠️ File contains database queries</div>";
            
            // Check if any of these queries might be problematic
            $lines = explode("\n", $content);
            foreach ($lines as $lineNum => $line) {
                if (preg_match('/\$db->.*?(?:fetchOne|fetchAll|query)/i', $line)) {
                    echo "<div class='info'>Line " . ($lineNum + 1) . ": <code>" . htmlspecialchars(trim($line)) . "</code></div>";
                }
            }
        } else {
            echo "<div class='success'>✅ No database queries found</div>";
        }
    }
}

echo "<h2>Test 6: Clear Everything and Test</h2>";

// Clear OPcache if available
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "<div class='info'>✅ OPcache cleared</div>";
}

// Clear session
session_destroy();
echo "<div class='info'>✅ Session cleared</div>";

// Test one more time
try {
    echo "<div class='info'>Final test with clean environment...</div>";
    
    unset($db);
    require 'config/config.php';
    
    if (isset($db)) {
        $result = $db->fetchOne("SELECT NOW() as current_time");
        echo "<div class='success'>🎉 SUCCESS! Everything works: " . $result['current_time'] . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Still failing: " . $e->getMessage() . "</div>";
    
    // If we're still getting the error, let's see the exact query being executed
    echo "<div class='info'>Let's check what query is actually being executed...</div>";
    
    // Enable MySQL query logging temporarily
    try {
        $pdo->query("SET GLOBAL general_log = 'ON'");
        $pdo->query("SET GLOBAL log_output = 'TABLE'");
        echo "<div class='info'>✅ MySQL query logging enabled</div>";
        
        // Try the failing operation again
        try {
            require 'config/config.php';
            if (isset($db)) {
                $db->fetchOne("SELECT NOW() as current_time");
            }
        } catch (Exception $e2) {
            // Ignore this error, we just want to log the query
        }
        
        // Check the query log
        $logs = $pdo->query("SELECT * FROM mysql.general_log WHERE command_type = 'Query' ORDER BY event_time DESC LIMIT 5")->fetchAll();
        
        echo "<div class='info'>Recent queries executed:</div>";
        foreach ($logs as $log) {
            echo "<div class='info'>Query: <code>" . htmlspecialchars($log['argument']) . "</code></div>";
        }
        
        // Disable logging
        $pdo->query("SET GLOBAL general_log = 'OFF'");
        
    } catch (Exception $e3) {
        echo "<div class='warning'>Could not enable query logging: " . $e3->getMessage() . "</div>";
    }
}

echo "<h2>Summary</h2>";
echo "<div class='info'>If you see the error above, it will help us identify exactly where the problematic SQL query is coming from.</div>";
?>
