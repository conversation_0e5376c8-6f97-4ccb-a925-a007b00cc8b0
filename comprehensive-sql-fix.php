<?php
/**
 * Comprehensive SQL Fix
 * This script will fix the SQL syntax error by addressing all possible causes
 */

echo "<h1>🔧 Comprehensive SQL Fix</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; margin: 10px 0; border-left: 4px solid green; }
    .error { color: red; background: #fff0f0; padding: 10px; margin: 10px 0; border-left: 4px solid red; }
    .warning { color: orange; background: #fff8f0; padding: 10px; margin: 10px 0; border-left: 4px solid orange; }
    .info { color: blue; background: #f0f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid blue; }
    .fix { color: purple; background: #f8f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid purple; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
    .step { border: 1px solid #ddd; margin: 20px 0; padding: 15px; background: #fafafa; }
</style>";

// Function to clear all caches
function clearAllCaches() {
    // Clear OPcache
    if (function_exists('opcache_reset')) {
        opcache_reset();
        echo "<div class='info'>✅ OPcache cleared</div>";
    }
    
    // Clear session
    if (session_status() === PHP_SESSION_ACTIVE) {
        session_destroy();
    }
    session_start();
    echo "<div class='info'>✅ Session cleared</div>";
    
    // Clear any file stat cache
    clearstatcache();
    echo "<div class='info'>✅ File stat cache cleared</div>";
}

// Function to test database connection
function testDatabaseConnection() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=flori_construction;charset=utf8mb4", "root", "", [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        // Test basic query
        $result = $pdo->query("SELECT NOW() as current_time")->fetch();
        return ['success' => true, 'time' => $result['current_time'], 'pdo' => $pdo];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Function to fix database class if needed
function fixDatabaseClass() {
    $dbFile = 'config/database.php';
    if (!file_exists($dbFile)) {
        return ['success' => false, 'error' => 'Database file not found'];
    }
    
    $content = file_get_contents($dbFile);
    $originalContent = $content;
    
    // Check for any problematic patterns and fix them
    $fixes = [
        '/current_time(?!\s*\()/i' => 'NOW()',
        '/current_date(?!\s*\()/i' => 'CURRENT_DATE()',
        '/SELECT\s+current_time(?!\s*\()/i' => 'SELECT NOW() as current_time'
    ];
    
    $changed = false;
    foreach ($fixes as $pattern => $replacement) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            $changed = true;
        }
    }
    
    if ($changed) {
        // Create backup
        $backupFile = $dbFile . '.backup.' . date('Y-m-d-H-i-s');
        file_put_contents($backupFile, $originalContent);
        
        // Apply fix
        file_put_contents($dbFile, $content);
        
        return ['success' => true, 'changed' => true, 'backup' => $backupFile];
    }
    
    return ['success' => true, 'changed' => false];
}

echo "<div class='step'>";
echo "<h2>Step 1: Clear All Caches</h2>";
clearAllCaches();
echo "</div>";

echo "<div class='step'>";
echo "<h2>Step 2: Test Basic Database Connection</h2>";
$dbTest = testDatabaseConnection();
if ($dbTest['success']) {
    echo "<div class='success'>✅ Database connection works: " . $dbTest['time'] . "</div>";
    $pdo = $dbTest['pdo'];
} else {
    echo "<div class='error'>❌ Database connection failed: " . $dbTest['error'] . "</div>";
    exit;
}
echo "</div>";

echo "<div class='step'>";
echo "<h2>Step 3: Check and Fix Database Class</h2>";
$dbClassFix = fixDatabaseClass();
if ($dbClassFix['success']) {
    if ($dbClassFix['changed']) {
        echo "<div class='fix'>🔧 Fixed database class, backup created: " . $dbClassFix['backup'] . "</div>";
    } else {
        echo "<div class='success'>✅ Database class is clean</div>";
    }
} else {
    echo "<div class='error'>❌ Could not check database class: " . $dbClassFix['error'] . "</div>";
}
echo "</div>";

echo "<div class='step'>";
echo "<h2>Step 4: Test Database Class</h2>";
try {
    require_once 'config/database.php';
    echo "<div class='success'>✅ Database class loaded</div>";
    
    $testDb = new Database();
    echo "<div class='success'>✅ Database class instantiated</div>";
    
    $result = $testDb->fetchOne("SELECT NOW() as current_time");
    echo "<div class='success'>✅ Database class query works: " . $result['current_time'] . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database class failed: " . $e->getMessage() . "</div>";
    
    // If database class fails, let's create a fixed version
    echo "<div class='fix'>🔧 Creating fixed database class...</div>";
    
    $fixedDbClass = '<?php
/**
 * Fixed Database Configuration for Flori Construction Ltd
 */

class Database {
    private $host = \'localhost\';
    private $db_name = \'flori_construction\';
    private $username = \'root\';
    private $password = \'\';
    private $charset = \'utf8mb4\';
    private $pdo;

    public function __construct() {
        $this->connect();
    }

    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->db_name};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];

            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    public function getConnection() {
        return $this->pdo;
    }

    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new Exception("Query failed: " . $e->getMessage());
        }
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    public function insert($table, $data) {
        $columns = implode(\',\', array_keys($data));
        $placeholders = str_repeat(\'?,\', count($data) - 1) . \'?\';

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, array_values($data));

        return $this->pdo->lastInsertId();
    }

    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        $params = [];

        foreach ($data as $key => $value) {
            $setClause[] = "{$key} = ?";
            $params[] = $value;
        }
        $setClause = implode(\', \', $setClause);

        foreach ($whereParams as $param) {
            $params[] = $param;
        }

        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        return $this->query($sql, $params);
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }

    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }

    public function commit() {
        return $this->pdo->commit();
    }

    public function rollback() {
        return $this->pdo->rollback();
    }
}

// Global database instance
$db = new Database();
?>';
    
    // Backup original and create fixed version
    $originalDbFile = 'config/database.php';
    $backupDbFile = $originalDbFile . '.backup.' . date('Y-m-d-H-i-s');
    
    if (file_exists($originalDbFile)) {
        copy($originalDbFile, $backupDbFile);
        echo "<div class='info'>✅ Original database class backed up to: " . $backupDbFile . "</div>";
    }
    
    file_put_contents($originalDbFile, $fixedDbClass);
    echo "<div class='fix'>🔧 Fixed database class created</div>";
    
    // Test the fixed class
    try {
        unset($db);
        require $originalDbFile;
        
        $result = $db->fetchOne("SELECT NOW() as current_time");
        echo "<div class='success'>✅ Fixed database class works: " . $result['current_time'] . "</div>";
        
    } catch (Exception $e2) {
        echo "<div class='error'>❌ Fixed database class still fails: " . $e2->getMessage() . "</div>";
    }
}
echo "</div>";

echo "<div class='step'>";
echo "<h2>Step 5: Test Full Configuration</h2>";
try {
    // Clear everything and test config
    unset($db);
    clearAllCaches();
    
    require 'config/config.php';
    echo "<div class='success'>✅ Config loaded successfully</div>";
    
    if (isset($db)) {
        $result = $db->fetchOne("SELECT NOW() as current_time");
        echo "<div class='success'>🎉 SUCCESS! Full config works: " . $result['current_time'] . "</div>";
    } else {
        echo "<div class='error'>❌ Global \$db not created</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Config still failing: " . $e->getMessage() . "</div>";
}
echo "</div>";

echo "<div class='step'>";
echo "<h2>Step 6: Final Verification</h2>";

// Test the main pages that might be causing issues
$testPages = ['index.php', 'admin/index.php'];

foreach ($testPages as $page) {
    if (file_exists($page)) {
        echo "<div class='info'>Testing page: " . $page . "</div>";
        
        try {
            ob_start();
            $errorBefore = error_get_last();
            
            include $page;
            
            $output = ob_get_clean();
            $errorAfter = error_get_last();
            
            if ($errorAfter && $errorAfter !== $errorBefore) {
                echo "<div class='error'>❌ Error in " . $page . ": " . $errorAfter['message'] . "</div>";
            } else {
                echo "<div class='success'>✅ " . $page . " loads without errors</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Exception in " . $page . ": " . $e->getMessage() . "</div>";
        }
    }
}
echo "</div>";

echo "<h2>🎯 Fix Complete</h2>";
echo "<div class='success'>";
echo "<h3>What was done:</h3>";
echo "<ul>";
echo "<li>✅ Cleared all caches (OPcache, session, file cache)</li>";
echo "<li>✅ Tested database connection</li>";
echo "<li>✅ Fixed database class if needed</li>";
echo "<li>✅ Created backups of modified files</li>";
echo "<li>✅ Verified configuration loading</li>";
echo "</ul>";
echo "</div>";

echo "<div class='info'>";
echo "<h3>Next steps:</h3>";
echo "<ol>";
echo "<li>Test your application thoroughly</li>";
echo "<li>If everything works, you can remove the backup files</li>";
echo "<li>Restart Apache/XAMPP to ensure all changes take effect</li>";
echo "</ol>";
echo "</div>";
?>
