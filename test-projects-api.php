<?php
/**
 * Simple test script to verify projects API functionality
 */

require_once 'config/config.php';

header('Content-Type: application/json');

// Test database connection
try {
    $testQuery = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_active = 1");
    $projectCount = $testQuery['count'];
    
    echo json_encode([
        'success' => true,
        'message' => 'Database connection successful',
        'project_count' => $projectCount,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
