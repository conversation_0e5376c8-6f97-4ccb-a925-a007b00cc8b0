<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App Diagnostic Tool</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e74c3c;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            background: #e74c3c;
            color: white;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #c0392b;
            transform: translateY(-1px);
        }
        .btn.secondary {
            background: #95a5a6;
        }
        .btn.success {
            background: #27ae60;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .result-item {
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #3498db;
            background: #ecf0f1;
            border-radius: 0 4px 4px 0;
        }
        .result-item.success { border-left-color: #27ae60; }
        .result-item.error { border-left-color: #e74c3c; }
        .result-item.warning { border-left-color: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-mobile-alt"></i> Mobile App Diagnostic Tool</h1>
            <p>Comprehensive testing and debugging for Flori Construction Mobile App</p>
        </div>

        <div id="overall-status" class="status info">
            <i class="fas fa-info-circle"></i> Ready to run diagnostics
        </div>

        <div class="grid">
            <div class="test-section">
                <h3><i class="fas fa-server"></i> System Tests</h3>
                <button class="btn" onclick="testEnvironment()">
                    <i class="fas fa-cog"></i> Test Environment
                </button>
                <button class="btn" onclick="testAPIEndpoints()">
                    <i class="fas fa-plug"></i> Test API Endpoints
                </button>
                <button class="btn" onclick="testDatabase()">
                    <i class="fas fa-database"></i> Test Database
                </button>
                <div id="system-results"></div>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-key"></i> Authentication Tests</h3>
                <button class="btn" onclick="testLogin()">
                    <i class="fas fa-sign-in-alt"></i> Test Login
                </button>
                <button class="btn" onclick="testTokenVerification()">
                    <i class="fas fa-shield-alt"></i> Test Token
                </button>
                <button class="btn secondary" onclick="clearAuth()">
                    <i class="fas fa-trash"></i> Clear Auth
                </button>
                <div id="auth-results"></div>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-building"></i> Projects Tests</h3>
                <button class="btn" onclick="testProjectsAPI()">
                    <i class="fas fa-list"></i> Test Projects API
                </button>
                <button class="btn" onclick="testProjectsManager()">
                    <i class="fas fa-cogs"></i> Test Manager
                </button>
                <button class="btn" onclick="testProjectCRUD()">
                    <i class="fas fa-edit"></i> Test CRUD
                </button>
                <div id="projects-results"></div>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-images"></i> Media Tests</h3>
                <button class="btn" onclick="testMediaAPI()">
                    <i class="fas fa-photo-video"></i> Test Media API
                </button>
                <button class="btn" onclick="testFileUpload()">
                    <i class="fas fa-upload"></i> Test Upload
                </button>
                <div id="media-results"></div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-play-circle"></i> Quick Actions</h3>
            <button class="btn success" onclick="runAllTests()">
                <i class="fas fa-rocket"></i> Run All Tests
            </button>
            <button class="btn secondary" onclick="clearLog()">
                <i class="fas fa-eraser"></i> Clear Log
            </button>
            <button class="btn secondary" onclick="exportResults()">
                <i class="fas fa-download"></i> Export Results
            </button>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> Debug Log</h3>
            <div id="debug-log" class="log-container">Diagnostic tool ready. Click "Run All Tests" to begin comprehensive testing.\n</div>
        </div>
    </div>

    <script>
        // Global variables
        let testResults = {};
        let apiBase = '';

        // Logging functions
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debug-log');
            const prefix = type.toUpperCase().padEnd(7);
            logElement.textContent += `[${timestamp}] [${prefix}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        function clearLog() {
            document.getElementById('debug-log').textContent = 'Log cleared.\n';
        }

        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('overall-status');
            statusElement.className = `status ${type}`;
            statusElement.innerHTML = `<i class="fas fa-${getStatusIcon(type)}"></i> ${message}`;
        }

        function getStatusIcon(type) {
            const icons = {
                success: 'check-circle',
                error: 'exclamation-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        function addResult(section, test, status, message) {
            if (!testResults[section]) testResults[section] = {};
            testResults[section][test] = { status, message };
            
            const resultElement = document.getElementById(`${section}-results`);
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${status}`;
            resultItem.innerHTML = `<strong>${test}:</strong> ${message}`;
            resultElement.appendChild(resultItem);
        }

        // Determine API base URL
        function getApiBaseUrl() {
            const currentLocation = window.location;
            const baseUrl = `${currentLocation.protocol}//${currentLocation.host}`;
            
            if (currentLocation.pathname.includes('/mobile-app/')) {
                return `${baseUrl}${currentLocation.pathname.replace('/mobile-app/', '/').replace(/\/[^\/]*$/, '')}/api`;
            }
            
            return `${baseUrl}/api`;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            apiBase = getApiBaseUrl();
            log(`Diagnostic tool initialized`);
            log(`API Base URL: ${apiBase}`);
            updateStatus('Diagnostic tool ready');
        });

        // Test functions
        async function testEnvironment() {
            log('=== ENVIRONMENT TEST START ===');
            clearResults('system');
            
            try {
                // Test browser capabilities
                const browserInfo = {
                    userAgent: navigator.userAgent,
                    online: navigator.onLine,
                    localStorage: typeof(Storage) !== "undefined",
                    fetch: typeof(fetch) !== "undefined",
                    serviceWorker: 'serviceWorker' in navigator
                };
                
                log(`Browser: ${browserInfo.userAgent}`);
                log(`Online: ${browserInfo.online}`);
                log(`LocalStorage: ${browserInfo.localStorage}`);
                log(`Fetch API: ${browserInfo.fetch}`);
                log(`Service Worker: ${browserInfo.serviceWorker}`);
                
                addResult('system', 'Browser Support', 'success', 'All required features supported');
                addResult('system', 'Network Status', browserInfo.online ? 'success' : 'warning', 
                         browserInfo.online ? 'Online' : 'Offline');
                
                log('Environment test completed successfully');
                
            } catch (error) {
                log(`Environment test error: ${error.message}`, 'error');
                addResult('system', 'Environment Test', 'error', error.message);
            }
            
            log('=== ENVIRONMENT TEST END ===');
        }

        async function testAPIEndpoints() {
            log('=== API ENDPOINTS TEST START ===');
            clearResults('system');
            
            const endpoints = [
                { url: `${apiBase}/auth.php?action=ping`, name: 'Auth API' },
                { url: `${apiBase}/mobile.php?action=ping`, name: 'Mobile API' }
            ];
            
            for (const endpoint of endpoints) {
                try {
                    log(`Testing ${endpoint.name}: ${endpoint.url}`);
                    
                    const response = await fetch(endpoint.url);
                    const data = await response.json();
                    
                    if (data.success) {
                        addResult('system', endpoint.name, 'success', 'Endpoint accessible');
                        log(`${endpoint.name} test passed`);
                    } else {
                        addResult('system', endpoint.name, 'warning', 'Endpoint responded with error');
                        log(`${endpoint.name} responded with error: ${data.error || 'Unknown'}`, 'warning');
                    }
                    
                } catch (error) {
                    addResult('system', endpoint.name, 'error', `Connection failed: ${error.message}`);
                    log(`${endpoint.name} test failed: ${error.message}`, 'error');
                }
            }
            
            log('=== API ENDPOINTS TEST END ===');
        }

        function clearResults(section) {
            const resultElement = document.getElementById(`${section}-results`);
            if (resultElement) {
                resultElement.innerHTML = '';
            }
        }

        // Placeholder functions for other tests
        async function testDatabase() {
            log('Database test not implemented yet');
            addResult('system', 'Database', 'warning', 'Test not implemented');
        }

        async function testLogin() {
            log('Login test not implemented yet');
            addResult('auth', 'Login', 'warning', 'Test not implemented');
        }

        async function testTokenVerification() {
            log('Token verification test not implemented yet');
            addResult('auth', 'Token Verification', 'warning', 'Test not implemented');
        }

        function clearAuth() {
            localStorage.removeItem('flori_token');
            localStorage.removeItem('flori_user');
            log('Authentication data cleared');
            addResult('auth', 'Clear Auth', 'success', 'Authentication data cleared');
        }

        async function testProjectsAPI() {
            log('Projects API test not implemented yet');
            addResult('projects', 'Projects API', 'warning', 'Test not implemented');
        }

        async function testProjectsManager() {
            log('Projects Manager test not implemented yet');
            addResult('projects', 'Projects Manager', 'warning', 'Test not implemented');
        }

        async function testProjectCRUD() {
            log('Project CRUD test not implemented yet');
            addResult('projects', 'CRUD Operations', 'warning', 'Test not implemented');
        }

        async function testMediaAPI() {
            log('Media API test not implemented yet');
            addResult('media', 'Media API', 'warning', 'Test not implemented');
        }

        async function testFileUpload() {
            log('File upload test not implemented yet');
            addResult('media', 'File Upload', 'warning', 'Test not implemented');
        }

        async function runAllTests() {
            log('=== RUNNING ALL TESTS ===');
            updateStatus('Running comprehensive tests...', 'info');
            
            await testEnvironment();
            await testAPIEndpoints();
            await testDatabase();
            
            log('=== ALL TESTS COMPLETED ===');
            updateStatus('All tests completed', 'success');
        }

        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                apiBase: apiBase,
                testResults: testResults,
                log: document.getElementById('debug-log').textContent
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `mobile-app-diagnostic-${new Date().toISOString().slice(0, 19)}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('Diagnostic results exported');
        }
    </script>
</body>
</html>
