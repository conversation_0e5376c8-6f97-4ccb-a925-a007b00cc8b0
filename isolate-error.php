<?php
/**
 * Isolate SQL Error
 * This script will test each component individually to find the exact source
 */

echo "<h1>SQL Error Isolation Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; margin: 10px 0; border-left: 4px solid green; }
    .error { color: red; background: #fff0f0; padding: 10px; margin: 10px 0; border-left: 4px solid red; }
    .warning { color: orange; background: #fff8f0; padding: 10px; margin: 10px 0; border-left: 4px solid orange; }
    .info { color: blue; background: #f0f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid blue; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
    .step { border: 1px solid #ddd; margin: 20px 0; padding: 15px; }
</style>";

// Step 1: Test raw PDO connection
echo "<div class='step'>";
echo "<h2>Step 1: Raw PDO Connection Test</h2>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=flori_construction;charset=utf8mb4", "root", "", [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "<div class='success'>✅ Raw PDO connection successful</div>";
    
    $result = $pdo->query("SELECT NOW() as NOW()")->fetch();
    echo "<div class='success'>✅ NOW() query works: " . $result['NOW()'] . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Raw PDO failed: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Step 2: Test Database class directly
echo "<div class='step'>";
echo "<h2>Step 2: Database Class Test</h2>";
try {
    // Include only the database class
    require_once 'config/database.php';
    echo "<div class='success'>✅ Database class loaded</div>";
    
    $testDb = new Database();
    echo "<div class='success'>✅ Database class instantiated</div>";
    
    $result = $testDb->fetchOne("SELECT NOW() as NOW()");
    echo "<div class='success'>✅ Database class query works: " . $result['NOW()'] . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database class failed: " . $e->getMessage() . "</div>";
    echo "<div class='info'>Error trace:</div>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
echo "</div>";

// Step 3: Test config loading without executing
echo "<div class='step'>";
echo "<h2>Step 3: Config File Analysis</h2>";
if (file_exists('config/config.php')) {
    $configContent = file_get_contents('config/config.php');
    echo "<div class='success'>✅ Config file exists and readable</div>";
    
    // Check for any immediate SQL execution in config
    if (preg_match('/\$db->.*?(?:fetchOne|fetchAll|query)/i', $configContent)) {
        echo "<div class='warning'>⚠️ Found database queries in config file</div>";
        
        $lines = explode("\n", $configContent);
        foreach ($lines as $lineNum => $line) {
            if (preg_match('/\$db->.*?(?:fetchOne|fetchAll|query)/i', $line)) {
                echo "<div class='info'>Line " . ($lineNum + 1) . ": <code>" . htmlspecialchars(trim($line)) . "</code></div>";
            }
        }
    } else {
        echo "<div class='success'>✅ No immediate database queries found in config</div>";
    }
    
    // Check for any problematic SQL patterns
    if (preg_match('/current_time(?!\s*\()/i', $configContent)) {
        echo "<div class='error'>❌ Found problematic 'NOW()' usage in config</div>";
        
        $lines = explode("\n", $configContent);
        foreach ($lines as $lineNum => $line) {
            if (preg_match('/current_time(?!\s*\()/i', $line)) {
                echo "<div class='error'>Line " . ($lineNum + 1) . ": <code>" . htmlspecialchars(trim($line)) . "</code></div>";
            }
        }
    } else {
        echo "<div class='success'>✅ No problematic SQL patterns in config</div>";
    }
} else {
    echo "<div class='error'>❌ Config file not found</div>";
}
echo "</div>";

// Step 4: Test config loading with error capture
echo "<div class='step'>";
echo "<h2>Step 4: Config Loading with Error Capture</h2>";
try {
    // Clear any previous errors
    error_clear_last();
    
    // Start output buffering to catch any output
    ob_start();
    
    // Set custom error handler to catch all errors
    $errors = [];
    set_error_handler(function($severity, $message, $file, $line) use (&$errors) {
        $errors[] = [
            'severity' => $severity,
            'message' => $message,
            'file' => $file,
            'line' => $line
        ];
    });
    
    // Try to include config
    include 'config/config.php';
    
    // Restore error handler
    restore_error_handler();
    
    $output = ob_get_clean();
    
    if (empty($errors)) {
        echo "<div class='success'>✅ Config loaded without PHP errors</div>";
    } else {
        echo "<div class='error'>❌ PHP errors during config loading:</div>";
        foreach ($errors as $error) {
            echo "<div class='error'>";
            echo "Severity: " . $error['severity'] . "<br>";
            echo "Message: " . htmlspecialchars($error['message']) . "<br>";
            echo "File: " . htmlspecialchars($error['file']) . "<br>";
            echo "Line: " . $error['line'] . "<br>";
            echo "</div>";
        }
    }
    
    if ($output) {
        echo "<div class='warning'>Output during config loading:</div>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
    }
    
    // Test if $db is available and working
    if (isset($db)) {
        echo "<div class='success'>✅ Global \$db variable created</div>";
        
        try {
            $result = $db->fetchOne("SELECT NOW() as NOW()");
            echo "<div class='success'>✅ Global \$db works: " . $result['NOW()'] . "</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Global \$db query failed: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='error'>❌ Global \$db variable not created</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Exception during config loading: " . $e->getMessage() . "</div>";
    echo "<div class='info'>Exception trace:</div>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
echo "</div>";

// Step 5: Check for any cached or session issues
echo "<div class='step'>";
echo "<h2>Step 5: Session and Cache Check</h2>";

// Check session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!empty($_SESSION)) {
    echo "<div class='info'>Session contains data</div>";
    $sessionStr = print_r($_SESSION, true);
    if (strpos($sessionStr, 'NOW()') !== false) {
        echo "<div class='warning'>⚠️ Session contains 'NOW()' references</div>";
        echo "<pre>" . htmlspecialchars($sessionStr) . "</pre>";
    } else {
        echo "<div class='success'>✅ Session clean of SQL references</div>";
    }
} else {
    echo "<div class='success'>✅ No session data</div>";
}

// Check for any opcache issues
if (function_exists('opcache_get_status')) {
    $opcacheStatus = opcache_get_status();
    if ($opcacheStatus && $opcacheStatus['opcache_enabled']) {
        echo "<div class='info'>OPcache is enabled - this might cache problematic code</div>";
        echo "<div class='warning'>Consider clearing OPcache: opcache_reset()</div>";
    } else {
        echo "<div class='success'>✅ OPcache not affecting execution</div>";
    }
} else {
    echo "<div class='success'>✅ OPcache not available</div>";
}

echo "</div>";

echo "<h2>Isolation Test Complete</h2>";
echo "<div class='info'>Based on the results above, we can determine:</div>";
echo "<ul>";
echo "<li>If Step 1 works but Step 2 fails: Issue is in the Database class</li>";
echo "<li>If Steps 1-2 work but Step 4 fails: Issue is in config.php or its includes</li>";
echo "<li>If all steps work: The error might be context-specific or cached</li>";
echo "</ul>";
?>
