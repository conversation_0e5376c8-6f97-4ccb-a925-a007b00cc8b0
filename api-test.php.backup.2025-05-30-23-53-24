<?php
/**
 * Simple API Test Script
 * Tests the mobile app API endpoints
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html><head><title>API Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    .btn { padding: 10px 15px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
</style></head><body>";

echo "<h1>🔌 API Test Results</h1>";

// Test 1: Basic PHP and Database Connection
echo "<div class='test-section'>";
echo "<h2>📊 Database Connection Test</h2>";

try {
    require_once 'config/config.php';
    echo "<p class='success'>✅ Config loaded successfully</p>";
    
    if (isset($db)) {
        echo "<p class='success'>✅ Database object created</p>";
        
        $result = $db->fetchOne("SELECT NOW() as current_time");
        echo "<p class='success'>✅ Database query successful: " . $result['current_time'] . "</p>";
        
        // Test users table
        $userCount = $db->fetchOne("SELECT COUNT(*) as count FROM users")['count'];
        echo "<p class='success'>✅ Users table accessible ($userCount users)</p>";
        
        // Test projects table
        $projectCount = $db->fetchOne("SELECT COUNT(*) as count FROM projects")['count'];
        echo "<p class='success'>✅ Projects table accessible ($projectCount projects)</p>";
        
    } else {
        echo "<p class='error'>❌ Database object not created</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test 2: Auth API
echo "<div class='test-section'>";
echo "<h2>🔑 Authentication API Test</h2>";

try {
    // Test ping endpoint
    $authPingUrl = "http://localhost:8080/api/auth.php?action=ping";
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($authPingUrl, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if ($data && isset($data['success']) && $data['success']) {
            echo "<p class='success'>✅ Auth API ping successful</p>";
            echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . "</pre>";
        } else {
            echo "<p class='warning'>⚠️ Auth API responded but with error</p>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
        }
    } else {
        echo "<p class='error'>❌ Auth API not accessible</p>";
    }
    
    // Test login endpoint with dummy data
    echo "<h3>Login Test</h3>";
    $loginData = json_encode(['username' => 'admin', 'password' => 'admin123']);
    $loginContext = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $loginData,
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);
    
    $loginResponse = @file_get_contents("http://localhost:8080/api/auth.php", false, $loginContext);
    
    if ($loginResponse !== false) {
        $loginData = json_decode($loginResponse, true);
        if ($loginData && isset($loginData['success'])) {
            if ($loginData['success']) {
                echo "<p class='success'>✅ Login test successful</p>";
                echo "<p>Token: " . substr($loginData['token'], 0, 20) . "...</p>";
                
                // Store token for further tests
                $testToken = $loginData['token'];
            } else {
                echo "<p class='warning'>⚠️ Login failed: " . ($loginData['error'] ?? 'Unknown error') . "</p>";
            }
        } else {
            echo "<p class='error'>❌ Invalid login response</p>";
            echo "<pre>" . htmlspecialchars($loginResponse) . "</pre>";
        }
    } else {
        echo "<p class='error'>❌ Login endpoint not accessible</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Auth API test error: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test 3: Mobile API
echo "<div class='test-section'>";
echo "<h2>📱 Mobile API Test</h2>";

try {
    // Test ping endpoint
    $mobilePingUrl = "http://localhost:8080/api/mobile.php?action=ping";
    $response = @file_get_contents($mobilePingUrl, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if ($data && isset($data['success']) && $data['success']) {
            echo "<p class='success'>✅ Mobile API ping successful</p>";
            echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . "</pre>";
        } else {
            echo "<p class='warning'>⚠️ Mobile API responded but with error</p>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
        }
    } else {
        echo "<p class='error'>❌ Mobile API not accessible</p>";
    }
    
    // Test projects endpoint (requires authentication)
    if (isset($testToken)) {
        echo "<h3>Projects API Test (Authenticated)</h3>";
        $projectsContext = stream_context_create([
            'http' => [
                'header' => "Authorization: Bearer $testToken",
                'timeout' => 5,
                'ignore_errors' => true
            ]
        ]);
        
        $projectsResponse = @file_get_contents("http://localhost:8080/api/mobile.php?action=projects", false, $projectsContext);
        
        if ($projectsResponse !== false) {
            $projectsData = json_decode($projectsResponse, true);
            if ($projectsData && isset($projectsData['success'])) {
                if ($projectsData['success']) {
                    $projectCount = count($projectsData['data']['projects'] ?? []);
                    echo "<p class='success'>✅ Projects API test successful ($projectCount projects)</p>";
                } else {
                    echo "<p class='warning'>⚠️ Projects API error: " . ($projectsData['error'] ?? 'Unknown error') . "</p>";
                }
            } else {
                echo "<p class='error'>❌ Invalid projects response</p>";
                echo "<pre>" . htmlspecialchars($projectsResponse) . "</pre>";
            }
        } else {
            echo "<p class='error'>❌ Projects endpoint not accessible</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Mobile API test error: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test 4: File System
echo "<div class='test-section'>";
echo "<h2>📁 File System Test</h2>";

$requiredDirs = ['uploads', 'assets', 'mobile-app'];
foreach ($requiredDirs as $dir) {
    if (is_dir($dir)) {
        echo "<p class='success'>✅ Directory '$dir' exists</p>";
        if (is_writable($dir)) {
            echo "<p class='success'>✅ Directory '$dir' is writable</p>";
        } else {
            echo "<p class='warning'>⚠️ Directory '$dir' is not writable</p>";
        }
    } else {
        echo "<p class='error'>❌ Directory '$dir' missing</p>";
    }
}

$requiredFiles = [
    'mobile-app/index.html',
    'mobile-app/js/app.js',
    'mobile-app/js/auth.js',
    'mobile-app/js/projects.js',
    'api/auth.php',
    'api/mobile.php'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ File '$file' exists</p>";
    } else {
        echo "<p class='error'>❌ File '$file' missing</p>";
    }
}

echo "</div>";

// Summary and Next Steps
echo "<div class='test-section'>";
echo "<h2>📋 Summary & Next Steps</h2>";

echo "<p><strong>Quick Links:</strong></p>";
echo "<ul>";
echo "<li><a href='mobile-app/index.html' class='btn'>Open Mobile App</a></li>";
echo "<li><a href='mobile-app/diagnostic.html' class='btn'>Open Diagnostic Tool</a></li>";
echo "<li><a href='mobile-app/test-projects-fixed.html' class='btn'>Open Test Page</a></li>";
echo "<li><a href='mobile-app-setup.php' class='btn'>Run Database Setup</a></li>";
echo "</ul>";

echo "<p><strong>Default Login Credentials:</strong></p>";
echo "<ul>";
echo "<li>Username: <code>admin</code></li>";
echo "<li>Password: <code>admin123</code></li>";
echo "</ul>";

echo "<p><strong>If you see errors above:</strong></p>";
echo "<ol>";
echo "<li>Make sure XAMPP is running</li>";
echo "<li>Ensure MySQL service is started</li>";
echo "<li>Run the database setup script</li>";
echo "<li>Check file permissions</li>";
echo "</ol>";

echo "</div>";

echo "</body></html>";
?>
