<?php
/**
 * Find SQL Error Script
 * This script will search for the exact source of the 'current_time' SQL error
 */

echo "<h1>SQL Error Source Finder</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; margin: 10px 0; border-left: 4px solid green; }
    .error { color: red; background: #fff0f0; padding: 10px; margin: 10px 0; border-left: 4px solid red; }
    .warning { color: orange; background: #fff8f0; padding: 10px; margin: 10px 0; border-left: 4px solid orange; }
    .info { color: blue; background: #f0f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid blue; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
    .file-content { background: #f9f9f9; padding: 10px; border: 1px solid #ccc; margin: 10px 0; }
</style>";

// Function to search for problematic SQL patterns
function searchForSQLPatterns($directory = '.') {
    $problematicPatterns = [
        '/SELECT\s+current_time(?!\s*\()/i',
        '/current_time(?!\s*\()/i',
        '/SELECT\s+current_time\s*$/i',
        '/current_time\s*$/i'
    ];
    
    $results = [];
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory));
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            $content = file_get_contents($file->getPathname());
            $lines = explode("\n", $content);
            
            foreach ($problematicPatterns as $pattern) {
                if (preg_match($pattern, $content)) {
                    foreach ($lines as $lineNum => $line) {
                        if (preg_match($pattern, $line)) {
                            $results[] = [
                                'file' => $file->getPathname(),
                                'line' => $lineNum + 1,
                                'content' => trim($line),
                                'pattern' => $pattern
                            ];
                        }
                    }
                }
            }
        }
    }
    
    return $results;
}

echo "<h2>Searching for Problematic SQL Patterns</h2>";

$results = searchForSQLPatterns();

if (empty($results)) {
    echo "<div class='success'>✅ No problematic SQL patterns found in PHP files</div>";
} else {
    echo "<div class='error'>❌ Found " . count($results) . " potential issues:</div>";
    
    foreach ($results as $result) {
        echo "<div class='warning'>";
        echo "<strong>File:</strong> " . htmlspecialchars($result['file']) . "<br>";
        echo "<strong>Line:</strong> " . $result['line'] . "<br>";
        echo "<strong>Content:</strong> <code>" . htmlspecialchars($result['content']) . "</code><br>";
        echo "<strong>Pattern:</strong> " . htmlspecialchars($result['pattern']) . "<br>";
        echo "</div>";
    }
}

// Check for any files that might be auto-executing SQL
echo "<h2>Checking for Auto-executing Files</h2>";

$autoExecuteFiles = [
    'config/config.php',
    'config/database.php',
    'index.php',
    'admin/index.php'
];

foreach ($autoExecuteFiles as $file) {
    if (file_exists($file)) {
        echo "<div class='info'>Checking: $file</div>";
        
        $content = file_get_contents($file);
        
        // Look for any SQL queries that might be executed immediately
        if (preg_match('/\$db->.*?current_time/i', $content)) {
            echo "<div class='error'>❌ Found database query with 'current_time' in $file</div>";
            
            // Show the specific lines
            $lines = explode("\n", $content);
            foreach ($lines as $lineNum => $line) {
                if (preg_match('/\$db->.*?current_time/i', $line)) {
                    echo "<div class='file-content'>";
                    echo "<strong>Line " . ($lineNum + 1) . ":</strong><br>";
                    echo "<code>" . htmlspecialchars(trim($line)) . "</code>";
                    echo "</div>";
                }
            }
        } else {
            echo "<div class='success'>✅ No problematic queries found in $file</div>";
        }
    }
}

// Check for any session or cached data that might contain the error
echo "<h2>Checking Session and Environment</h2>";

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!empty($_SESSION)) {
    echo "<div class='info'>Session data exists, checking for SQL-related content...</div>";
    $sessionData = print_r($_SESSION, true);
    if (strpos($sessionData, 'current_time') !== false) {
        echo "<div class='warning'>⚠️ Found 'current_time' in session data</div>";
        echo "<pre>" . htmlspecialchars($sessionData) . "</pre>";
    } else {
        echo "<div class='success'>✅ No SQL-related content in session</div>";
    }
} else {
    echo "<div class='success'>✅ No session data</div>";
}

// Check for any error logs or temporary files
echo "<h2>Checking for Error Sources</h2>";

$errorSources = [
    'error_log',
    'php_errors.log',
    'apache_error.log',
    'mysql_error.log'
];

foreach ($errorSources as $logFile) {
    if (file_exists($logFile)) {
        echo "<div class='info'>Found log file: $logFile</div>";
        $content = file_get_contents($logFile);
        if (strpos($content, 'current_time') !== false) {
            echo "<div class='warning'>⚠️ Found 'current_time' references in $logFile</div>";
            // Show last few lines that contain the error
            $lines = explode("\n", $content);
            $relevantLines = array_filter($lines, function($line) {
                return strpos($line, 'current_time') !== false;
            });
            
            if (!empty($relevantLines)) {
                echo "<div class='file-content'>";
                echo "<strong>Recent errors:</strong><br>";
                foreach (array_slice($relevantLines, -5) as $line) {
                    echo "<code>" . htmlspecialchars($line) . "</code><br>";
                }
                echo "</div>";
            }
        }
    }
}

// Test if the error occurs when loading specific files
echo "<h2>Testing Individual File Loading</h2>";

$testFiles = [
    'config/config.php',
    'config/database.php'
];

foreach ($testFiles as $file) {
    if (file_exists($file)) {
        echo "<div class='info'>Testing: $file</div>";
        
        try {
            ob_start();
            $errorBefore = error_get_last();
            
            include $file;
            
            $output = ob_get_clean();
            $errorAfter = error_get_last();
            
            if ($errorAfter && $errorAfter !== $errorBefore) {
                echo "<div class='error'>❌ Error occurred while loading $file:</div>";
                echo "<pre>" . htmlspecialchars(print_r($errorAfter, true)) . "</pre>";
            } else {
                echo "<div class='success'>✅ $file loaded without errors</div>";
            }
            
            if ($output) {
                echo "<div class='warning'>Output from $file:</div>";
                echo "<pre>" . htmlspecialchars($output) . "</pre>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Exception while loading $file: " . $e->getMessage() . "</div>";
        }
    }
}

echo "<h2>Analysis Complete</h2>";
echo "<div class='info'>If no issues were found above, the error might be:</div>";
echo "<ul>";
echo "<li>Coming from a database trigger or stored procedure</li>";
echo "<li>Generated by a cached query or session</li>";
echo "<li>Occurring in a file that's not being checked</li>";
echo "<li>Related to a specific user action or form submission</li>";
echo "</ul>";

echo "<div class='info'>To get more information, try:</div>";
echo "<ul>";
echo "<li>Clear your browser cache and cookies</li>";
echo "<li>Restart your web server (Apache/XAMPP)</li>";
echo "<li>Check the MySQL error logs</li>";
echo "<li>Enable MySQL query logging to see the exact query being executed</li>";
echo "</ul>";
?>
