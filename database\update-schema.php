<?php
/**
 * Database Schema Update Script
 * Run this to add new tables and columns for enhanced functionality
 */

require_once '../config/config.php';

echo "<h2>Database Schema Update</h2>\n";
echo "<p>Adding new tables and columns for enhanced functionality...</p>\n";

try {
    // Create site_settings table
    $sql = "CREATE TABLE IF NOT EXISTS site_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(255) UNIQUE NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT NOW()STAMP,
        updated_at TIMESTAMP DEFAULT NOW()STAMP ON UPDATE NOW()STAMP
    )";
    
    $db->query($sql);
    echo "✅ Created site_settings table<br>\n";
    
    // Create site_content table
    $sql = "CREATE TABLE IF NOT EXISTS site_content (
        id INT AUTO_INCREMENT PRIMARY KEY,
        content_key VARCHAR(255) UNIQUE NOT NULL,
        content_value TEXT,
        created_at TIMESTAMP DEFAULT NOW()STAMP,
        updated_at TIMESTAMP DEFAULT NOW()STAMP ON UPDATE NOW()STAMP
    )";
    
    $db->query($sql);
    echo "✅ Created site_content table<br>\n";
    
    // Create page_seo table
    $sql = "CREATE TABLE IF NOT EXISTS page_seo (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_key VARCHAR(255) UNIQUE NOT NULL,
        meta_title VARCHAR(255),
        meta_description TEXT,
        meta_keywords TEXT,
        created_at TIMESTAMP DEFAULT NOW()STAMP,
        updated_at TIMESTAMP DEFAULT NOW()STAMP ON UPDATE NOW()STAMP
    )";
    
    $db->query($sql);
    echo "✅ Created page_seo table<br>\n";
    
    // Check if contact_inquiries table needs updates
    $columns = $db->fetchAll("SHOW COLUMNS FROM contact_inquiries");
    $columnNames = array_column($columns, 'Field');
    
    // Add missing columns to contact_inquiries
    if (!in_array('service_interest', $columnNames)) {
        $db->query("ALTER TABLE contact_inquiries ADD COLUMN service_interest VARCHAR(255) AFTER message");
        echo "✅ Added service_interest column to contact_inquiries<br>\n";
    }
    
    if (!in_array('project_reference', $columnNames)) {
        $db->query("ALTER TABLE contact_inquiries ADD COLUMN project_reference VARCHAR(255) AFTER service_interest");
        echo "✅ Added project_reference column to contact_inquiries<br>\n";
    }
    
    if (!in_array('ip_address', $columnNames)) {
        $db->query("ALTER TABLE contact_inquiries ADD COLUMN ip_address VARCHAR(45) AFTER project_reference");
        echo "✅ Added ip_address column to contact_inquiries<br>\n";
    }
    
    if (!in_array('user_agent', $columnNames)) {
        $db->query("ALTER TABLE contact_inquiries ADD COLUMN user_agent TEXT AFTER ip_address");
        echo "✅ Added user_agent column to contact_inquiries<br>\n";
    }
    
    // Insert default content if not exists
    $defaultContent = [
        'hero_title' => 'Building Excellence Since 2010',
        'hero_subtitle' => 'Professional construction services with unmatched quality and reliability',
        'about_title' => 'About Flori Construction Ltd',
        'about_text' => 'Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.',
        'services_title' => 'Our Services',
        'services_subtitle' => 'Comprehensive construction solutions for all your needs',
        'projects_title' => 'Our Projects',
        'projects_subtitle' => 'Showcasing our commitment to excellence and quality craftsmanship',
        'contact_title' => 'Get In Touch',
        'contact_subtitle' => 'Ready to start your construction project? Contact us for a free consultation and quote.',
        'footer_text' => 'Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.'
    ];
    
    foreach ($defaultContent as $key => $value) {
        $existing = $db->fetchOne("SELECT id FROM site_content WHERE content_key = ?", [$key]);
        if (!$existing) {
            $db->insert('site_content', [
                'content_key' => $key,
                'content_value' => $value,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            echo "✅ Added default content for: $key<br>\n";
        }
    }
    
    // Insert default branding settings
    $defaultBranding = [
        'branding_colors' => json_encode([
            'primary_color' => '#e74c3c',
            'secondary_color' => '#2c3e50',
            'accent_color' => '#f39c12',
            'text_color' => '#333333',
            'background_color' => '#ffffff'
        ]),
        'branding_fonts' => json_encode([
            'heading_font' => 'Oswald',
            'body_font' => 'Roboto'
        ]),
        'email_settings' => json_encode([
            'smtp_enabled' => false,
            'smtp_host' => 'smtp.gmail.com',
            'smtp_port' => 587,
            'smtp_username' => '',
            'smtp_password' => '',
            'from_email' => SITE_EMAIL,
            'from_name' => SITE_NAME,
            'auto_reply_enabled' => true,
            'notification_email' => SITE_EMAIL
        ])
    ];
    
    foreach ($defaultBranding as $key => $value) {
        $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = ?", [$key]);
        if (!$existing) {
            $db->insert('site_settings', [
                'setting_key' => $key,
                'setting_value' => $value,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            echo "✅ Added default setting for: $key<br>\n";
        }
    }
    
    // Insert default SEO settings
    $defaultSEO = [
        'home' => [
            'meta_title' => SITE_NAME . ' - Professional Construction Services in London',
            'meta_description' => 'Professional construction services in London. Specializing in civil engineering, groundworks, RC frames, basements, and hard landscaping.',
            'meta_keywords' => 'construction, london, civil engineering, groundworks, basements, landscaping'
        ],
        'about' => [
            'meta_title' => 'About Us - ' . SITE_NAME,
            'meta_description' => 'Learn about Flori Construction Ltd, our experience, and commitment to quality construction services in London.',
            'meta_keywords' => 'about, construction company, london, experience, quality'
        ],
        'services' => [
            'meta_title' => 'Our Services - ' . SITE_NAME,
            'meta_description' => 'Comprehensive construction services including civil engineering, groundworks, RC frames, basements, and hard landscaping.',
            'meta_keywords' => 'construction services, civil engineering, groundworks, basements, landscaping'
        ],
        'projects' => [
            'meta_title' => 'Our Projects - ' . SITE_NAME,
            'meta_description' => 'View our portfolio of completed construction projects showcasing quality craftsmanship and attention to detail.',
            'meta_keywords' => 'construction projects, portfolio, completed works, quality craftsmanship'
        ],
        'contact' => [
            'meta_title' => 'Contact Us - ' . SITE_NAME,
            'meta_description' => 'Get in touch with Flori Construction Ltd for your construction needs. Contact us for quotes and consultations.',
            'meta_keywords' => 'contact, construction quotes, consultation, london construction'
        ]
    ];
    
    foreach ($defaultSEO as $page => $seoData) {
        $existing = $db->fetchOne("SELECT id FROM page_seo WHERE page_key = ?", [$page]);
        if (!$existing) {
            $db->insert('page_seo', array_merge($seoData, [
                'page_key' => $page,
                'created_at' => date('Y-m-d H:i:s')
            ]));
            echo "✅ Added default SEO for: $page page<br>\n";
        }
    }
    
    echo "<br><h3>✅ Database update completed successfully!</h3>\n";
    echo "<p>All new tables and columns have been added. You can now use the enhanced features:</p>\n";
    echo "<ul>\n";
    echo "<li>🎨 Branding customization (colors, fonts, logo)</li>\n";
    echo "<li>📧 Email notifications and testing</li>\n";
    echo "<li>📝 Content management system</li>\n";
    echo "<li>🔍 SEO meta tag management</li>\n";
    echo "<li>📊 Enhanced contact form tracking</li>\n";
    echo "</ul>\n";
    echo "<p><a href='../admin/index.php'>Go to Admin Panel</a></p>\n";
    
} catch (Exception $e) {
    echo "<h3>❌ Error updating database:</h3>\n";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>\n";
    echo "<p>Please check your database connection and try again.</p>\n";
}
?>
