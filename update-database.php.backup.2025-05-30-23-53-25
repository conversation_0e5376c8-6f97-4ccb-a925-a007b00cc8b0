<?php
/**
 * Database Update Script
 * Adds missing tables and features for high-priority implementations
 */

require_once 'config/config.php';

echo "🔄 Starting database update...\n\n";

try {
    // Check if push_subscriptions table exists
    $result = $db->query("SHOW TABLES LIKE 'push_subscriptions'");
    if ($result->rowCount() == 0) {
        echo "📱 Creating push_subscriptions table...\n";
        $db->query("
            CREATE TABLE push_subscriptions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                endpoint TEXT NOT NULL,
                p256dh_key TEXT NOT NULL,
                auth_key TEXT NOT NULL,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_active (user_id, is_active)
            )
        ");
        echo "✅ push_subscriptions table created\n";
    } else {
        echo "✅ push_subscriptions table already exists\n";
    }

    // Check if notification_history table exists
    $result = $db->query("SHOW TABLES LIKE 'notification_history'");
    if ($result->rowCount() == 0) {
        echo "📝 Creating notification_history table...\n";
        $db->query("
            CREATE TABLE notification_history (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                body TEXT NOT NULL,
                data JSON,
                sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at TIMESTAMP NULL,
                sent_by INT,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (sent_by) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_user_sent (user_id, sent_at),
                INDEX idx_read_status (user_id, read_at)
            )
        ");
        echo "✅ notification_history table created\n";
    } else {
        echo "✅ notification_history table already exists\n";
    }

    // Check if testimonials table exists and has all required columns
    $result = $db->query("SHOW TABLES LIKE 'testimonials'");
    if ($result->rowCount() == 0) {
        echo "⭐ Creating testimonials table...\n";
        $db->query("
            CREATE TABLE testimonials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                client_name VARCHAR(255) NOT NULL,
                company VARCHAR(255),
                rating INT NOT NULL DEFAULT 5,
                review TEXT NOT NULL,
                project_id INT,
                is_featured TINYINT(1) DEFAULT 0,
                is_approved TINYINT(1) DEFAULT 1,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL,
                INDEX idx_featured (is_featured),
                INDEX idx_approved (is_approved),
                INDEX idx_sort (sort_order)
            )
        ");
        echo "✅ testimonials table created\n";
        
        // Add sample testimonials
        echo "📝 Adding sample testimonials...\n";
        $sampleTestimonials = [
            [
                'client_name' => 'John Smith',
                'company' => 'Smith Enterprises',
                'rating' => 5,
                'review' => 'Flori Construction did an excellent job on our office renovation. Professional, timely, and high-quality work. Highly recommended!',
                'is_featured' => 1,
                'is_approved' => 1,
                'sort_order' => 1
            ],
            [
                'client_name' => 'Sarah Johnson',
                'company' => 'Johnson & Associates',
                'rating' => 5,
                'review' => 'Outstanding service from start to finish. The team was professional, clean, and delivered exactly what was promised. Will definitely use them again.',
                'is_featured' => 1,
                'is_approved' => 1,
                'sort_order' => 2
            ],
            [
                'client_name' => 'Michael Brown',
                'company' => '',
                'rating' => 4,
                'review' => 'Great experience with Flori Construction. They completed our home extension on time and within budget. Very satisfied with the results.',
                'is_featured' => 0,
                'is_approved' => 1,
                'sort_order' => 3
            ]
        ];
        
        foreach ($sampleTestimonials as $testimonial) {
            $db->insert('testimonials', $testimonial);
        }
        echo "✅ Sample testimonials added\n";
    } else {
        echo "✅ testimonials table already exists\n";
    }

    // Check if users table has all required columns
    echo "👥 Checking users table structure...\n";
    $columns = $db->fetchAll("SHOW COLUMNS FROM users");
    $columnNames = array_column($columns, 'Field');
    
    $requiredColumns = ['last_login', 'is_active'];
    foreach ($requiredColumns as $column) {
        if (!in_array($column, $columnNames)) {
            echo "📝 Adding missing column: $column\n";
            if ($column === 'last_login') {
                $db->query("ALTER TABLE users ADD COLUMN last_login TIMESTAMP NULL AFTER updated_at");
            } elseif ($column === 'is_active') {
                $db->query("ALTER TABLE users ADD COLUMN is_active TINYINT(1) DEFAULT 1 AFTER role");
            }
            echo "✅ Column $column added\n";
        }
    }

    // Update existing users to be active
    $db->query("UPDATE users SET is_active = 1 WHERE is_active IS NULL");

    // Check if admin user exists
    $adminUser = $db->fetchOne("SELECT id FROM users WHERE username = 'admin'");
    if (!$adminUser) {
        echo "👤 Creating default admin user...\n";
        $db->insert('users', [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
            'full_name' => 'Administrator',
            'role' => 'admin',
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        echo "✅ Default admin user created (username: admin, password: admin123)\n";
        echo "⚠️  Please change the default password after login!\n";
    } else {
        echo "✅ Admin user already exists\n";
    }

    // Check database statistics
    echo "\n📊 Database Statistics:\n";
    
    $stats = [
        'users' => $db->fetchOne("SELECT COUNT(*) as count FROM users")['count'],
        'projects' => $db->fetchOne("SELECT COUNT(*) as count FROM projects")['count'],
        'services' => $db->fetchOne("SELECT COUNT(*) as count FROM services")['count'],
        'testimonials' => $db->fetchOne("SELECT COUNT(*) as count FROM testimonials")['count'],
        'media' => $db->fetchOne("SELECT COUNT(*) as count FROM media")['count'],
        'contact_inquiries' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries")['count']
    ];
    
    foreach ($stats as $table => $count) {
        echo "  - $table: $count records\n";
    }

    echo "\n✅ Database update completed successfully!\n\n";
    
    echo "🚀 Next Steps:\n";
    echo "1. Login to admin panel: http://localhost/erdevwe/admin/\n";
    echo "2. Test new features:\n";
    echo "   - User Management: /admin/users.php\n";
    echo "   - Testimonials: /admin/testimonials.php\n";
    echo "   - Profile Management: /admin/profile.php\n";
    echo "3. Test new API endpoints:\n";
    echo "   - Content API: /api/content.php\n";
    echo "   - Services API: /api/services.php\n";
    echo "   - Users API: /api/users.php\n";
    echo "   - Inquiries API: /api/inquiries.php\n";
    echo "   - Notifications API: /api/notifications.php\n";
    echo "4. Configure push notifications in mobile app\n";
    echo "5. Update security settings in config/config.php\n\n";

} catch (Exception $e) {
    echo "❌ Error updating database: " . $e->getMessage() . "\n";
    echo "Please check your database connection and try again.\n";
}
?>
