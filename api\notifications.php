<?php
/**
 * Push Notifications API
 * Handles push notification subscriptions and sending notifications
 */

require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    // All notification operations require authentication
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Authentication required'], 401);
    }
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action']) && $_GET['action'] === 'history') {
                handleGetNotificationHistory($user);
            } else {
                handleGetSubscription($user);
            }
            break;
            
        case 'POST':
            $action = $input['action'] ?? '';
            if ($action === 'subscribe') {
                handleSubscribe($input, $user);
            } elseif ($action === 'send') {
                handleSendNotification($input, $user);
            } else {
                jsonResponse(['error' => 'Invalid action'], 400);
            }
            break;
            
        case 'PUT':
            $action = $input['action'] ?? '';
            if ($action === 'mark_read') {
                handleMarkAsRead($input, $user);
            } else {
                jsonResponse(['error' => 'Invalid action'], 400);
            }
            break;
            
        case 'DELETE':
            handleUnsubscribe($user);
            break;
            
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['error' => $e->getMessage()], 500);
}

function handleGetSubscription($user) {
    global $db;
    
    try {
        $subscription = $db->fetchOne(
            "SELECT * FROM push_subscriptions WHERE user_id = ? AND is_active = 1",
            [$user['id']]
        );
        
        jsonResponse([
            'success' => true,
            'subscribed' => $subscription !== null,
            'subscription' => $subscription
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to get subscription: ' . $e->getMessage()], 500);
    }
}

function handleSubscribe($input, $user) {
    global $db;
    
    if (!isset($input['subscription'])) {
        jsonResponse(['error' => 'Subscription data is required'], 400);
    }
    
    $subscription = $input['subscription'];
    
    // Validate subscription data
    if (!isset($subscription['endpoint']) || !isset($subscription['keys'])) {
        jsonResponse(['error' => 'Invalid subscription data'], 400);
    }
    
    try {
        // Check if subscription already exists
        $existing = $db->fetchOne(
            "SELECT id FROM push_subscriptions WHERE user_id = ? AND endpoint = ?",
            [$user['id'], $subscription['endpoint']]
        );
        
        if ($existing) {
            // Update existing subscription
            $db->update('push_subscriptions', [
                'p256dh_key' => $subscription['keys']['p256dh'],
                'auth_key' => $subscription['keys']['auth'],
                'is_active' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$existing['id']]);
            
            $message = 'Subscription updated successfully';
        } else {
            // Create new subscription
            $db->insert('push_subscriptions', [
                'user_id' => $user['id'],
                'endpoint' => $subscription['endpoint'],
                'p256dh_key' => $subscription['keys']['p256dh'],
                'auth_key' => $subscription['keys']['auth'],
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            $message = 'Subscription created successfully';
        }
        
        jsonResponse([
            'success' => true,
            'message' => $message
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to save subscription: ' . $e->getMessage()], 500);
    }
}

function handleUnsubscribe($user) {
    global $db;
    
    try {
        $db->update('push_subscriptions', [
            'is_active' => 0,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'user_id = ?', [$user['id']]);
        
        jsonResponse([
            'success' => true,
            'message' => 'Unsubscribed successfully'
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to unsubscribe: ' . $e->getMessage()], 500);
    }
}

function handleSendNotification($input, $user) {
    global $db;
    
    // Only admins can send notifications
    if ($user['role'] !== 'admin') {
        jsonResponse(['error' => 'Admin access required'], 403);
    }
    
    $title = $input['title'] ?? 'Flori Construction';
    $body = $input['body'] ?? '';
    $targetUserId = $input['user_id'] ?? null;
    $data = $input['data'] ?? [];
    
    if (empty($body)) {
        jsonResponse(['error' => 'Notification body is required'], 400);
    }
    
    try {
        // Get target subscriptions
        $whereClause = 'is_active = 1';
        $params = [];
        
        if ($targetUserId) {
            $whereClause .= ' AND user_id = ?';
            $params[] = $targetUserId;
        }
        
        $subscriptions = $db->fetchAll(
            "SELECT * FROM push_subscriptions WHERE $whereClause",
            $params
        );
        
        if (empty($subscriptions)) {
            jsonResponse(['error' => 'No active subscriptions found'], 404);
        }
        
        $sentCount = 0;
        $failedCount = 0;
        
        foreach ($subscriptions as $subscription) {
            try {
                $success = sendPushNotification($subscription, $title, $body, $data);
                if ($success) {
                    $sentCount++;
                    
                    // Log notification
                    $db->insert('notification_history', [
                        'user_id' => $subscription['user_id'],
                        'title' => $title,
                        'body' => $body,
                        'data' => json_encode($data),
                        'sent_at' => date('Y-m-d H:i:s'),
                        'sent_by' => $user['id']
                    ]);
                } else {
                    $failedCount++;
                }
            } catch (Exception $e) {
                $failedCount++;
                error_log('Push notification failed: ' . $e->getMessage());
            }
        }
        
        jsonResponse([
            'success' => true,
            'message' => "Notification sent to $sentCount users",
            'sent_count' => $sentCount,
            'failed_count' => $failedCount
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to send notification: ' . $e->getMessage()], 500);
    }
}

function handleGetNotificationHistory($user) {
    global $db;
    
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    
    try {
        // Get user's notification history
        $notifications = $db->fetchAll(
            "SELECT nh.*, u.full_name as sent_by_name 
             FROM notification_history nh 
             LEFT JOIN users u ON nh.sent_by = u.id 
             WHERE nh.user_id = ? 
             ORDER BY nh.sent_at DESC 
             LIMIT $limit OFFSET $offset",
            [$user['id']]
        );
        
        // Get total count
        $total = $db->fetchOne(
            "SELECT COUNT(*) as total FROM notification_history WHERE user_id = ?",
            [$user['id']]
        )['total'];
        
        jsonResponse([
            'success' => true,
            'notifications' => $notifications,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to get notification history: ' . $e->getMessage()], 500);
    }
}

function handleMarkAsRead($input, $user) {
    global $db;
    
    if (!isset($input['notification_id'])) {
        jsonResponse(['error' => 'Notification ID is required'], 400);
    }
    
    $notificationId = (int)$input['notification_id'];
    
    try {
        $db->update('notification_history', [
            'read_at' => date('Y-m-d H:i:s')
        ], 'id = ? AND user_id = ?', [$notificationId, $user['id']]);
        
        jsonResponse([
            'success' => true,
            'message' => 'Notification marked as read'
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to mark notification as read: ' . $e->getMessage()], 500);
    }
}

function sendPushNotification($subscription, $title, $body, $data = []) {
    // This is a simplified implementation
    // In production, you would use a proper push notification service like Firebase FCM
    // or implement Web Push Protocol with VAPID keys
    
    $payload = json_encode([
        'title' => $title,
        'body' => $body,
        'icon' => '/erdevwe/assets/images/logo-192.png',
        'badge' => '/erdevwe/assets/images/logo-72.png',
        'data' => $data
    ]);
    
    // For now, just log the notification (implement actual push sending in production)
    error_log("Push notification would be sent to: " . $subscription['endpoint']);
    error_log("Payload: " . $payload);
    
    return true; // Return true for demo purposes
}

// Include authentication function from auth.php
function authenticateRequest() {
    global $db;

    $token = getBearerToken();

    if (!$token) {
        return false;
    }

    $hashedToken = hash('sha256', $token);

    // Get token and user info
    $result = $db->fetchOne(
        "SELECT u.id, u.username, u.email, u.full_name, u.role, u.is_active, t.expires_at
         FROM users u
         JOIN api_tokens t ON u.id = t.user_id
         WHERE t.token = ? AND t.is_active = 1 AND u.is_active = 1",
        [$hashedToken]
    );

    if (!$result) {
        return false;
    }

    // Check if token is expired
    if (strtotime($result['expires_at']) < time()) {
        // Deactivate expired token
        $db->update('api_tokens',
            ['is_active' => 0],
            'token = ?',
            [$hashedToken]
        );
        return false;
    }

    return $result;
}

function getBearerToken() {
    $headers = getallheaders();
    
    if (isset($headers['Authorization'])) {
        $matches = [];
        if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
            return $matches[1];
        }
    }
    
    return null;
}
?>
