/**
 * Authentication JavaScript for Flori Construction Admin App
 * Handles login, logout, and token management
 */

class AuthManager {
    constructor() {
        // Use the same API base as FloriAdmin
        this.apiBase = window.floriAdmin?.apiBase || this.getApiBaseUrl();
        this.setupEventListeners();
    }

    getApiBaseUrl() {
        // Get current location and construct API URL
        const currentLocation = window.location;
        const baseUrl = `${currentLocation.protocol}//${currentLocation.host}`;

        // Check if we're in mobile-app directory
        if (currentLocation.pathname.includes('/mobile-app/')) {
            return `${baseUrl}${currentLocation.pathname.replace('/mobile-app/', '/').replace(/\/[^\/]*$/, '')}/api`;
        }

        // Default fallback
        return `${baseUrl}/api`;
    }

    setupEventListeners() {
        // Login form
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin(e.target);
            });
        }

        // Profile form
        const profileForm = document.getElementById('profile-form');
        if (profileForm) {
            profileForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleProfileUpdate(e.target);
            });
        }

        // Password form
        const passwordForm = document.getElementById('password-form');
        if (passwordForm) {
            passwordForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handlePasswordChange(e.target);
            });
        }
    }

    async handleLogin(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const errorDiv = document.getElementById('login-error');

        // Get form values
        const username = formData.get('username');
        const password = formData.get('password');

        // Validate inputs
        if (!username || !password) {
            this.showLoginError('Please enter both username and password');
            return;
        }

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
        submitBtn.disabled = true;

        // Hide previous errors
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }

        try {
            const response = await fetch(`${this.apiBase}/auth.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });

            const data = await response.json();

            if (data.success) {
                // Store token and user data
                localStorage.setItem('flori_token', data.token);
                localStorage.setItem('flori_user', JSON.stringify(data.user));

                // Update app state
                if (window.floriAdmin) {
                    window.floriAdmin.token = data.token;
                    window.floriAdmin.user = data.user;
                    window.floriAdmin.showMainApp();
                    window.floriAdmin.loadDashboard();
                }

                // Show success message
                this.showToast('Login successful!', 'success');

                // Clear form
                form.reset();

            } else {
                this.showLoginError(data.error || 'Login failed');
            }

        } catch (error) {
            console.error('Login error:', error);
            this.showLoginError('Network error. Please try again.');
        } finally {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    async handleProfileUpdate(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');

        // Get form values
        const fullName = formData.get('full_name');
        const email = formData.get('email');

        // Validate inputs
        if (!fullName || !email) {
            this.showToast('Please fill in all fields', 'error');
            return;
        }

        if (!this.isValidEmail(email)) {
            this.showToast('Please enter a valid email address', 'error');
            return;
        }

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
        submitBtn.disabled = true;

        try {
            const response = await window.floriAdmin.apiRequest('users.php?action=update-profile', {
                method: 'PUT',
                body: JSON.stringify({
                    full_name: fullName,
                    email: email
                })
            });

            if (response && response.success) {
                // Update stored user data
                const updatedUser = { ...window.floriAdmin.user, ...response.user };
                localStorage.setItem('flori_user', JSON.stringify(updatedUser));
                window.floriAdmin.user = updatedUser;

                this.showToast('Profile updated successfully!', 'success');
            } else {
                this.showToast(response?.error || 'Failed to update profile', 'error');
            }

        } catch (error) {
            console.error('Profile update error:', error);
            this.showToast('Network error. Please try again.', 'error');
        } finally {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    async handlePasswordChange(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');

        // Get form values
        const currentPassword = formData.get('current_password');
        const newPassword = formData.get('new_password');
        const confirmPassword = formData.get('confirm_password');

        // Validate inputs
        if (!currentPassword || !newPassword || !confirmPassword) {
            this.showToast('Please fill in all fields', 'error');
            return;
        }

        if (newPassword !== confirmPassword) {
            this.showToast('New passwords do not match', 'error');
            return;
        }

        if (newPassword.length < 6) {
            this.showToast('New password must be at least 6 characters long', 'error');
            return;
        }

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Changing...';
        submitBtn.disabled = true;

        try {
            const response = await window.floriAdmin.apiRequest('users.php?action=change-password', {
                method: 'PUT',
                body: JSON.stringify({
                    current_password: currentPassword,
                    new_password: newPassword
                })
            });

            if (response && response.success) {
                this.showToast('Password changed successfully!', 'success');
                form.reset();
            } else {
                this.showToast(response?.error || 'Failed to change password', 'error');
            }

        } catch (error) {
            console.error('Password change error:', error);
            this.showToast('Network error. Please try again.', 'error');
        } finally {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    showLoginError(message) {
        const errorDiv = document.getElementById('login-error');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    }

    showToast(message, type = 'info') {
        if (window.floriAdmin) {
            window.floriAdmin.showToast(message, type);
        }
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    async refreshToken() {
        const currentToken = localStorage.getItem('flori_token');
        if (!currentToken) return false;

        try {
            const response = await fetch(`${this.apiBase}/auth.php?action=refresh`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${currentToken}`
                }
            });

            const data = await response.json();

            if (data.success) {
                localStorage.setItem('flori_token', data.token);
                if (window.floriAdmin) {
                    window.floriAdmin.token = data.token;
                }
                return true;
            }

            return false;
        } catch (error) {
            console.error('Token refresh failed:', error);
            return false;
        }
    }

    async logout() {
        const token = localStorage.getItem('flori_token');

        // Call logout API
        if (token) {
            try {
                await fetch(`${this.apiBase}/auth.php?action=logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
            } catch (error) {
                console.error('Logout API call failed:', error);
            }
        }

        // Clear local storage
        localStorage.removeItem('flori_token');
        localStorage.removeItem('flori_user');

        // Update app state
        if (window.floriAdmin) {
            window.floriAdmin.token = null;
            window.floriAdmin.user = null;
            window.floriAdmin.showLogin();
        }

        this.showToast('Logged out successfully', 'success');
    }

    // Auto-refresh token before expiry
    startTokenRefreshTimer() {
        // Refresh token every 25 days (5 days before 30-day expiry)
        const refreshInterval = 25 * 24 * 60 * 60 * 1000; // 25 days in milliseconds

        setInterval(async () => {
            const success = await this.refreshToken();
            if (!success) {
                console.log('Token refresh failed, user will need to login again');
            }
        }, refreshInterval);
    }

    // Check if user is authenticated
    isAuthenticated() {
        const token = localStorage.getItem('flori_token');
        const user = localStorage.getItem('flori_user');
        return !!(token && user);
    }

    // Get current user
    getCurrentUser() {
        const userStr = localStorage.getItem('flori_user');
        return userStr ? JSON.parse(userStr) : null;
    }

    // Get current token
    getToken() {
        return localStorage.getItem('flori_token');
    }
}

// Initialize auth manager
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();

    // Start token refresh timer if user is authenticated
    if (window.authManager.isAuthenticated()) {
        window.authManager.startTokenRefreshTimer();
    }
});

// Handle keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + L for logout
    if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
        e.preventDefault();
        if (window.authManager && window.authManager.isAuthenticated()) {
            window.authManager.logout();
        }
    }

    // Enter key on login form
    if (e.key === 'Enter' && document.getElementById('login-screen').style.display !== 'none') {
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.dispatchEvent(new Event('submit'));
        }
    }
});

// Handle online/offline status
window.addEventListener('online', () => {
    if (window.floriAdmin) {
        window.floriAdmin.showToast('Connection restored', 'success');
    }
});

window.addEventListener('offline', () => {
    if (window.floriAdmin) {
        window.floriAdmin.showToast('Connection lost. Some features may not work.', 'warning');
    }
});
