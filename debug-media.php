<?php
/**
 * Debug Media Upload Issues
 * This script helps diagnose media upload problems
 */

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Media Upload - Flori Construction</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        .test-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔍 Media Upload Debug Tool</h1>
    <p>This tool helps diagnose and fix media upload issues.</p>

    <?php
    try {
        require_once 'config/config.php';
        echo "<div class='success'>✅ Database connection successful</div>";
        
        // Test 1: Check upload directory
        echo "<div class='test-section'>";
        echo "<h2>Test 1: Upload Directory Check</h2>";
        
        $uploadDir = 'uploads';
        $fullUploadPath = __DIR__ . '/' . $uploadDir;
        
        if (!file_exists($fullUploadPath)) {
            echo "<div class='error'>❌ Upload directory doesn't exist: $fullUploadPath</div>";
            echo "<div class='info'>Creating upload directory...</div>";
            
            if (mkdir($fullUploadPath, 0755, true)) {
                echo "<div class='success'>✅ Upload directory created successfully</div>";
            } else {
                echo "<div class='error'>❌ Failed to create upload directory</div>";
            }
        } else {
            echo "<div class='success'>✅ Upload directory exists: $fullUploadPath</div>";
        }
        
        // Check permissions
        if (is_writable($fullUploadPath)) {
            echo "<div class='success'>✅ Upload directory is writable</div>";
        } else {
            echo "<div class='error'>❌ Upload directory is not writable</div>";
            echo "<div class='info'>Try running: chmod 755 $fullUploadPath</div>";
        }
        
        // Check subdirectories
        $subdirs = ['projects', 'services', 'gallery', 'general'];
        foreach ($subdirs as $subdir) {
            $subdirPath = $fullUploadPath . '/' . $subdir;
            if (!file_exists($subdirPath)) {
                if (mkdir($subdirPath, 0755, true)) {
                    echo "<div class='success'>✅ Created subdirectory: $subdir</div>";
                } else {
                    echo "<div class='error'>❌ Failed to create subdirectory: $subdir</div>";
                }
            } else {
                echo "<div class='success'>✅ Subdirectory exists: $subdir</div>";
            }
        }
        echo "</div>";
        
        // Test 2: Check PHP upload settings
        echo "<div class='test-section'>";
        echo "<h2>Test 2: PHP Upload Configuration</h2>";
        
        $uploadMaxFilesize = ini_get('upload_max_filesize');
        $postMaxSize = ini_get('post_max_size');
        $maxFileUploads = ini_get('max_file_uploads');
        $maxExecutionTime = ini_get('max_execution_time');
        $memoryLimit = ini_get('memory_limit');
        
        echo "<div class='info'>";
        echo "<strong>PHP Upload Settings:</strong><br>";
        echo "upload_max_filesize: $uploadMaxFilesize<br>";
        echo "post_max_size: $postMaxSize<br>";
        echo "max_file_uploads: $maxFileUploads<br>";
        echo "max_execution_time: $maxExecutionTime seconds<br>";
        echo "memory_limit: $memoryLimit<br>";
        echo "</div>";
        
        // Convert to bytes for comparison
        function convertToBytes($value) {
            $value = trim($value);
            $last = strtolower($value[strlen($value)-1]);
            $value = (int) $value;
            switch($last) {
                case 'g': $value *= 1024;
                case 'm': $value *= 1024;
                case 'k': $value *= 1024;
            }
            return $value;
        }
        
        $uploadBytes = convertToBytes($uploadMaxFilesize);
        $postBytes = convertToBytes($postMaxSize);
        
        if ($uploadBytes >= 10 * 1024 * 1024) { // 10MB
            echo "<div class='success'>✅ upload_max_filesize is adequate ($uploadMaxFilesize)</div>";
        } else {
            echo "<div class='warning'>⚠️ upload_max_filesize might be too small ($uploadMaxFilesize)</div>";
        }
        
        if ($postBytes >= $uploadBytes) {
            echo "<div class='success'>✅ post_max_size is adequate ($postMaxSize)</div>";
        } else {
            echo "<div class='error'>❌ post_max_size ($postMaxSize) should be larger than upload_max_filesize ($uploadMaxFilesize)</div>";
        }
        echo "</div>";
        
        // Test 3: Check media table
        echo "<div class='test-section'>";
        echo "<h2>Test 3: Media Database Table</h2>";
        
        $tables = $db->fetchAll("SHOW TABLES LIKE 'media'");
        if (empty($tables)) {
            echo "<div class='error'>❌ Media table doesn't exist</div>";
            echo "<div class='info'>Creating media table...</div>";
            
            $createTableSQL = "CREATE TABLE media (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL,
                original_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_type ENUM('image', 'video') NOT NULL,
                file_size INT NOT NULL,
                mime_type VARCHAR(100) NOT NULL,
                title VARCHAR(255),
                description TEXT,
                alt_text VARCHAR(255),
                category ENUM('projects', 'services', 'gallery', 'general') DEFAULT 'general',
                uploaded_by INT,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT NOW()STAMP,
                updated_at TIMESTAMP DEFAULT NOW()STAMP ON UPDATE NOW()STAMP,
                FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
            )";
            
            try {
                $db->query($createTableSQL);
                echo "<div class='success'>✅ Media table created successfully</div>";
            } catch (Exception $e) {
                echo "<div class='error'>❌ Failed to create media table: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='success'>✅ Media table exists</div>";
            
            // Check current media files
            $mediaCount = $db->fetchOne("SELECT COUNT(*) as count FROM media")['count'];
            echo "<div class='info'>Current media files in database: $mediaCount</div>";
            
            if ($mediaCount > 0) {
                $recentMedia = $db->fetchAll("SELECT filename, original_name, file_type, category, created_at FROM media ORDER BY created_at DESC LIMIT 5");
                echo "<div class='info'><strong>Recent uploads:</strong></div>";
                echo "<ul>";
                foreach ($recentMedia as $media) {
                    echo "<li>{$media['original_name']} ({$media['file_type']}) - {$media['category']} - {$media['created_at']}</li>";
                }
                echo "</ul>";
            }
        }
        echo "</div>";
        
        // Test 4: Test file upload function
        echo "<div class='test-section'>";
        echo "<h2>Test 4: Upload Function Test</h2>";
        
        if (function_exists('uploadFile')) {
            echo "<div class='success'>✅ uploadFile function exists</div>";
        } else {
            echo "<div class='error'>❌ uploadFile function not found</div>";
            echo "<div class='info'>The uploadFile function should be defined in config/functions.php</div>";
        }
        
        // Check if GD extension is loaded (for image processing)
        if (extension_loaded('gd')) {
            echo "<div class='success'>✅ GD extension loaded (image processing available)</div>";
        } else {
            echo "<div class='warning'>⚠️ GD extension not loaded (image thumbnails may not work)</div>";
        }
        
        // Check if fileinfo extension is loaded
        if (extension_loaded('fileinfo')) {
            echo "<div class='success'>✅ Fileinfo extension loaded (MIME type detection available)</div>";
        } else {
            echo "<div class='warning'>⚠️ Fileinfo extension not loaded (MIME type detection may not work)</div>";
        }
        echo "</div>";
        
        // Test 5: Check recent PHP errors
        echo "<div class='test-section'>";
        echo "<h2>Test 5: Recent PHP Errors</h2>";
        
        $errorLog = ini_get('error_log');
        if ($errorLog && file_exists($errorLog)) {
            echo "<div class='info'>PHP error log: $errorLog</div>";
            
            // Read last 20 lines of error log
            $lines = file($errorLog);
            $recentLines = array_slice($lines, -20);
            
            $mediaErrors = array_filter($recentLines, function($line) {
                return strpos($line, 'media') !== false || strpos($line, 'upload') !== false;
            });
            
            if (!empty($mediaErrors)) {
                echo "<div class='warning'>Recent media/upload related errors:</div>";
                echo "<pre>" . implode('', $mediaErrors) . "</pre>";
            } else {
                echo "<div class='success'>✅ No recent media/upload related errors found</div>";
            }
        } else {
            echo "<div class='info'>PHP error log not found or not configured</div>";
        }
        echo "</div>";
        
        // Test 6: Manual upload test
        echo "<div class='test-section'>";
        echo "<h2>Test 6: Manual Upload Test</h2>";
        
        echo "<div class='info'>You can test file upload manually here:</div>";
        echo "<form method='POST' enctype='multipart/form-data'>";
        echo "<input type='file' name='test_file' accept='image/*'>";
        echo "<button type='submit' name='test_upload' class='btn'>Test Upload</button>";
        echo "</form>";
        
        if (isset($_POST['test_upload']) && isset($_FILES['test_file'])) {
            echo "<div class='info'>Testing file upload...</div>";
            
            $file = $_FILES['test_file'];
            echo "<pre>";
            echo "File details:\n";
            echo "Name: " . $file['name'] . "\n";
            echo "Type: " . $file['type'] . "\n";
            echo "Size: " . $file['size'] . " bytes\n";
            echo "Error: " . $file['error'] . "\n";
            echo "Tmp name: " . $file['tmp_name'] . "\n";
            echo "</pre>";
            
            if ($file['error'] === UPLOAD_ERR_OK) {
                $destination = $fullUploadPath . '/test_' . time() . '_' . $file['name'];
                if (move_uploaded_file($file['tmp_name'], $destination)) {
                    echo "<div class='success'>✅ Test file uploaded successfully to: $destination</div>";
                    
                    // Clean up test file
                    if (file_exists($destination)) {
                        unlink($destination);
                        echo "<div class='info'>Test file cleaned up</div>";
                    }
                } else {
                    echo "<div class='error'>❌ Failed to move uploaded file</div>";
                }
            } else {
                echo "<div class='error'>❌ Upload error code: " . $file['error'] . "</div>";
                
                $uploadErrors = [
                    UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
                    UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE',
                    UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
                    UPLOAD_ERR_NO_FILE => 'No file was uploaded',
                    UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
                    UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                    UPLOAD_ERR_EXTENSION => 'Upload stopped by extension'
                ];
                
                if (isset($uploadErrors[$file['error']])) {
                    echo "<div class='error'>Error: " . $uploadErrors[$file['error']] . "</div>";
                }
            }
        }
        echo "</div>";
        
        // Summary and recommendations
        echo "<div class='test-section'>";
        echo "<h2>✅ Diagnosis Complete</h2>";
        echo "<div class='success'>Media upload system diagnosis completed!</div>";
        
        echo "<h3>Quick Fixes:</h3>";
        echo "<p>";
        echo "<a href='admin/media.php' class='btn'>🚀 Try Media Upload Again</a>";
        echo "<a href='admin/media.php?action=upload' class='btn'>📁 Upload Files</a>";
        echo "<a href='setup.php' class='btn'>🔧 Run Database Setup</a>";
        echo "</p>";
        
        echo "<h3>Common Solutions:</h3>";
        echo "<ul>";
        echo "<li><strong>Permission Issues:</strong> Make sure uploads/ directory has 755 permissions</li>";
        echo "<li><strong>File Size:</strong> Check that your files are under " . $uploadMaxFilesize . "</li>";
        echo "<li><strong>File Type:</strong> Only upload JPG, PNG, GIF, WebP images or MP4, MOV, AVI videos</li>";
        echo "<li><strong>Database:</strong> Make sure the media table exists (run setup.php if needed)</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
        echo "<div class='info'>Stack trace:</div>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>

    <h2>What This Tool Checks</h2>
    <div class="info">
        <p>This diagnostic tool checks:</p>
        <ul>
            <li>✅ Upload directory exists and is writable</li>
            <li>✅ PHP upload configuration is adequate</li>
            <li>✅ Media database table exists</li>
            <li>✅ Required PHP extensions are loaded</li>
            <li>✅ Recent error logs for upload issues</li>
            <li>✅ Manual file upload test</li>
        </ul>
        
        <p><strong>If you're still having issues after running this tool, the problem might be:</strong></p>
        <ul>
            <li>JavaScript errors in the browser (check browser console)</li>
            <li>Form validation issues</li>
            <li>Session or authentication problems</li>
            <li>Server configuration issues</li>
        </ul>
    </div>
</body>
</html>
