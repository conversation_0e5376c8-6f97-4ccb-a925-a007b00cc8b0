<?php
/**
 * Test Database Connection and Setup
 */

echo "<h1>Database Connection Test</h1>";

try {
    // Test basic PHP database connection
    $host = 'localhost';
    $dbname = 'flori_construction';
    $username = 'root';
    $password = '';
    
    echo "<h2>Testing Database Connection...</h2>";
    
    $dsn = "mysql:host=$host;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "<p style='color: green;'>✅ MySQL connection successful</p>";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE 'flori_construction'");
    $dbExists = $stmt->fetch();
    
    if ($dbExists) {
        echo "<p style='color: green;'>✅ Database 'flori_construction' exists</p>";
        
        // Connect to the specific database
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        // Check required tables
        $requiredTables = ['users', 'projects', 'api_tokens', 'media'];
        echo "<h3>Checking Required Tables:</h3>";
        
        foreach ($requiredTables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            $tableExists = $stmt->fetch();
            
            if ($tableExists) {
                echo "<p style='color: green;'>✅ Table '$table' exists</p>";
                
                // Check table data
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $count = $stmt->fetch()['count'];
                echo "<p style='margin-left: 20px;'>Records: $count</p>";
                
                if ($table === 'users') {
                    $stmt = $pdo->query("SELECT username, email, role FROM users LIMIT 5");
                    $users = $stmt->fetchAll();
                    echo "<p style='margin-left: 20px;'>Users:</p>";
                    foreach ($users as $user) {
                        echo "<p style='margin-left: 40px;'>- {$user['username']} ({$user['email']}) - {$user['role']}</p>";
                    }
                }
            } else {
                echo "<p style='color: red;'>❌ Table '$table' missing</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ Database 'flori_construction' does not exist</p>";
        echo "<p>Creating database...</p>";
        
        $pdo->exec("CREATE DATABASE flori_construction");
        echo "<p style='color: green;'>✅ Database created</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
    
    if (strpos($e->getMessage(), 'Connection refused') !== false) {
        echo "<p style='color: orange;'>⚠️ MySQL server appears to be down. Please start XAMPP/MySQL.</p>";
    }
}

echo "<h2>Testing Config File</h2>";

if (file_exists('config/config.php')) {
    echo "<p style='color: green;'>✅ Config file exists</p>";
    
    try {
        require_once 'config/config.php';
        echo "<p style='color: green;'>✅ Config file loaded successfully</p>";
        
        if (isset($db)) {
            echo "<p style='color: green;'>✅ Database object created</p>";
            
            // Test a simple query
            $result = $db->fetchOne("SELECT NOW() as current_time");
            echo "<p style='color: green;'>✅ Database query successful: " . $result['current_time'] . "</p>";
        } else {
            echo "<p style='color: red;'>❌ Database object not created</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Config error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Config file not found</p>";
}

echo "<h2>Next Steps</h2>";
echo "<p>If database is missing or tables are missing:</p>";
echo "<ol>";
echo "<li>Make sure XAMPP is running</li>";
echo "<li>Run <a href='setup.php'>setup.php</a> to create database and tables</li>";
echo "<li>Or run <a href='update-database.php'>update-database.php</a> to update existing database</li>";
echo "</ol>";

?>
