<?php
/**
 * Automatic SQL Error Fix
 * This script will automatically identify and fix the SQL syntax error
 */

echo "<h1>🔧 Automatic SQL Error Fix</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; margin: 10px 0; border-left: 4px solid green; }
    .error { color: red; background: #fff0f0; padding: 10px; margin: 10px 0; border-left: 4px solid red; }
    .warning { color: orange; background: #fff8f0; padding: 10px; margin: 10px 0; border-left: 4px solid orange; }
    .info { color: blue; background: #f0f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid blue; }
    .fix { color: purple; background: #f8f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid purple; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
    .step { border: 1px solid #ddd; margin: 20px 0; padding: 15px; background: #fafafa; }
</style>";

// Function to scan for problematic SQL patterns
function findProblematicSQL($directory = '.') {
    $issues = [];
    $patterns = [
        '/SELECT\s+current_time(?!\s*\()/i',
        '/INSERT.*?current_time(?!\s*\()/i',
        '/UPDATE.*?current_time(?!\s*\()/i',
        '/WHERE.*?current_time(?!\s*\()/i',
        '/\$db->.*?["\'].*?current_time(?!\s*\()["\'].*?\)/i'
    ];
    
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory));
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            $filePath = $file->getPathname();
            $content = file_get_contents($filePath);
            $lines = explode("\n", $content);
            
            foreach ($patterns as $pattern) {
                foreach ($lines as $lineNum => $line) {
                    if (preg_match($pattern, $line)) {
                        $issues[] = [
                            'file' => $filePath,
                            'line' => $lineNum + 1,
                            'content' => trim($line),
                            'pattern' => $pattern
                        ];
                    }
                }
            }
        }
    }
    
    return $issues;
}

// Function to fix SQL syntax in a file
function fixSQLSyntax($filePath) {
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Define replacement patterns
    $replacements = [
        '/SELECT\s+current_time(?!\s*\()/i' => 'SELECT NOW() as NOW()',
        '/current_time(?!\s*\()/i' => 'NOW()',
        '/current_date(?!\s*\()/i' => 'CURRENT_DATE()',
        '/NOW()stamp(?!\s*\()/i' => 'NOW()STAMP'
    ];
    
    foreach ($replacements as $pattern => $replacement) {
        $content = preg_replace($pattern, $replacement, $content);
    }
    
    return [
        'changed' => $content !== $originalContent,
        'content' => $content,
        'original' => $originalContent
    ];
}

echo "<div class='step'>";
echo "<h2>Step 1: Scanning for SQL Syntax Issues</h2>";

$issues = findProblematicSQL();

if (empty($issues)) {
    echo "<div class='success'>✅ No obvious SQL syntax issues found in PHP files</div>";
} else {
    echo "<div class='error'>❌ Found " . count($issues) . " potential SQL syntax issues:</div>";
    
    foreach ($issues as $issue) {
        echo "<div class='warning'>";
        echo "<strong>File:</strong> " . htmlspecialchars($issue['file']) . "<br>";
        echo "<strong>Line:</strong> " . $issue['line'] . "<br>";
        echo "<strong>Content:</strong> <code>" . htmlspecialchars($issue['content']) . "</code><br>";
        echo "</div>";
    }
}
echo "</div>";

echo "<div class='step'>";
echo "<h2>Step 2: Testing Database Connection</h2>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=flori_construction;charset=utf8mb4", "root", "", [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "<div class='success'>✅ Database connection successful</div>";
    
    // Test the problematic query
    try {
        $badResult = $pdo->query("SELECT NOW() as NOW()")->fetch();
        echo "<div class='warning'>⚠️ Problematic query worked (unexpected)</div>";
    } catch (Exception $e) {
        echo "<div class='info'>✅ Confirmed: 'SELECT NOW() as NOW()' fails as expected</div>";
        echo "<div class='info'>Error: " . $e->getMessage() . "</div>";
    }
    
    // Test the correct query
    $goodResult = $pdo->query("SELECT NOW() as NOW()")->fetch();
    echo "<div class='success'>✅ Correct query works: " . $goodResult['NOW()'] . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database connection failed: " . $e->getMessage() . "</div>";
}
echo "</div>";

echo "<div class='step'>";
echo "<h2>Step 3: Testing Config Loading</h2>";

try {
    // Test config loading with error capture
    ob_start();
    $errorBefore = error_get_last();
    
    require_once 'config/config.php';
    
    $output = ob_get_clean();
    $errorAfter = error_get_last();
    
    if ($errorAfter && $errorAfter !== $errorBefore) {
        echo "<div class='error'>❌ Error during config loading:</div>";
        echo "<pre>" . htmlspecialchars(print_r($errorAfter, true)) . "</pre>";
    } else {
        echo "<div class='success'>✅ Config loaded without errors</div>";
    }
    
    if ($output) {
        echo "<div class='warning'>Output during config loading:</div>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
    }
    
    // Test the global $db object
    if (isset($db)) {
        echo "<div class='success'>✅ Global \$db object created</div>";
        
        try {
            $result = $db->fetchOne("SELECT NOW() as NOW()");
            echo "<div class='success'>✅ Global \$db query works: " . $result['NOW()'] . "</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Global \$db query failed: " . $e->getMessage() . "</div>";
            echo "<div class='fix'>🔧 This is likely where your error is occurring!</div>";
        }
    } else {
        echo "<div class='error'>❌ Global \$db object not created</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Config loading failed: " . $e->getMessage() . "</div>";
    echo "<div class='fix'>🔧 The error is in the config file or its dependencies</div>";
}
echo "</div>";

echo "<div class='step'>";
echo "<h2>Step 4: Automatic Fix Application</h2>";

if (!empty($issues)) {
    echo "<div class='fix'>🔧 Applying automatic fixes...</div>";
    
    $filesToFix = array_unique(array_column($issues, 'file'));
    
    foreach ($filesToFix as $file) {
        echo "<div class='info'>Processing: " . htmlspecialchars($file) . "</div>";
        
        $fixResult = fixSQLSyntax($file);
        
        if ($fixResult['changed']) {
            // Create backup
            $backupFile = $file . '.backup.' . date('Y-m-d-H-i-s');
            file_put_contents($backupFile, $fixResult['original']);
            echo "<div class='info'>✅ Backup created: " . htmlspecialchars($backupFile) . "</div>";
            
            // Apply fix
            file_put_contents($file, $fixResult['content']);
            echo "<div class='success'>✅ Fixed SQL syntax in: " . htmlspecialchars($file) . "</div>";
        } else {
            echo "<div class='info'>ℹ️ No changes needed for: " . htmlspecialchars($file) . "</div>";
        }
    }
} else {
    echo "<div class='info'>ℹ️ No files need automatic fixing</div>";
}
echo "</div>";

echo "<div class='step'>";
echo "<h2>Step 5: Verification Test</h2>";

try {
    echo "<div class='info'>Testing config loading after fixes...</div>";
    
    // Clear any cached includes
    if (function_exists('opcache_reset')) {
        opcache_reset();
        echo "<div class='info'>✅ OPcache cleared</div>";
    }
    
    // Test config loading again
    ob_start();
    $errorBefore = error_get_last();
    
    // Force reload by using a different variable name
    unset($db);
    require 'config/config.php';
    
    $output = ob_get_clean();
    $errorAfter = error_get_last();
    
    if ($errorAfter && $errorAfter !== $errorBefore) {
        echo "<div class='error'>❌ Still getting errors after fix:</div>";
        echo "<pre>" . htmlspecialchars(print_r($errorAfter, true)) . "</pre>";
    } else {
        echo "<div class='success'>✅ Config loads without errors after fix</div>";
    }
    
    if (isset($db)) {
        try {
            $result = $db->fetchOne("SELECT NOW() as NOW()");
            echo "<div class='success'>🎉 SUCCESS! Database queries work: " . $result['NOW()'] . "</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Database queries still failing: " . $e->getMessage() . "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Verification failed: " . $e->getMessage() . "</div>";
}
echo "</div>";

echo "<h2>🎯 Fix Summary</h2>";
echo "<div class='success'>";
echo "<h3>What was fixed:</h3>";
echo "<ul>";
echo "<li>Replaced <code>NOW()</code> with <code>NOW()</code></li>";
echo "<li>Replaced <code>CURRENT_DATE()</code> with <code>CURRENT_DATE()</code></li>";
echo "<li>Replaced <code>NOW()stamp</code> with <code>NOW()STAMP</code></li>";
echo "<li>Fixed malformed SELECT statements</li>";
echo "</ul>";
echo "</div>";

echo "<div class='info'>";
echo "<h3>Next steps:</h3>";
echo "<ol>";
echo "<li>Test your application to ensure it works correctly</li>";
echo "<li>Remove backup files once you're satisfied with the fixes</li>";
echo "<li>Clear browser cache and restart Apache if needed</li>";
echo "</ol>";
echo "</div>";
?>
