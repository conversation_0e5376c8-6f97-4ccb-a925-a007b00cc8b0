<?php
/**
 * Rate Limiting System for API Protection
 * Flori Construction Ltd
 */

class RateLimiter {
    private $db;
    private $maxRequests;
    private $timeWindow;
    
    public function __construct($database, $maxRequests = 100, $timeWindow = 3600) {
        $this->db = $database;
        $this->maxRequests = $maxRequests;
        $this->timeWindow = $timeWindow; // 1 hour default
        $this->createTable();
    }
    
    private function createTable() {
        $sql = "CREATE TABLE IF NOT EXISTS rate_limits (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL,
            endpoint VARCHAR(255) NOT NULL,
            request_count INT DEFAULT 1,
            window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_request TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_ip_endpoint (ip_address, endpoint),
            INDEX idx_window_start (window_start)
        )";
        
        try {
            $this->db->query($sql);
        } catch (Exception $e) {
            error_log("Rate limiter table creation failed: " . $e->getMessage());
        }
    }
    
    public function checkLimit($endpoint = null) {
        $ipAddress = $this->getClientIP();
        $endpoint = $endpoint ?: $_SERVER['REQUEST_URI'];
        
        // Clean old records
        $this->cleanOldRecords();
        
        // Check current rate
        $currentTime = time();
        $windowStart = $currentTime - $this->timeWindow;
        
        $existing = $this->db->fetchOne(
            "SELECT * FROM rate_limits 
             WHERE ip_address = ? AND endpoint = ? 
             AND window_start > FROM_UNIXTIME(?)",
            [$ipAddress, $endpoint, $windowStart]
        );
        
        if ($existing) {
            if ($existing['request_count'] >= $this->maxRequests) {
                return [
                    'allowed' => false,
                    'remaining' => 0,
                    'reset_time' => strtotime($existing['window_start']) + $this->timeWindow
                ];
            }
            
            // Update count
            $this->db->query(
                "UPDATE rate_limits SET request_count = request_count + 1 
                 WHERE id = ?",
                [$existing['id']]
            );
            
            return [
                'allowed' => true,
                'remaining' => $this->maxRequests - ($existing['request_count'] + 1),
                'reset_time' => strtotime($existing['window_start']) + $this->timeWindow
            ];
        } else {
            // Create new record
            $this->db->query(
                "INSERT INTO rate_limits (ip_address, endpoint, request_count, window_start) 
                 VALUES (?, ?, 1, FROM_UNIXTIME(?))",
                [$ipAddress, $endpoint, $currentTime]
            );
            
            return [
                'allowed' => true,
                'remaining' => $this->maxRequests - 1,
                'reset_time' => $currentTime + $this->timeWindow
            ];
        }
    }
    
    public function enforceLimit($endpoint = null) {
        $result = $this->checkLimit($endpoint);
        
        if (!$result['allowed']) {
            http_response_code(429);
            header('Content-Type: application/json');
            header('X-RateLimit-Limit: ' . $this->maxRequests);
            header('X-RateLimit-Remaining: 0');
            header('X-RateLimit-Reset: ' . $result['reset_time']);
            header('Retry-After: ' . ($result['reset_time'] - time()));
            
            echo json_encode([
                'error' => 'Rate limit exceeded',
                'message' => 'Too many requests. Please try again later.',
                'retry_after' => $result['reset_time'] - time()
            ]);
            exit;
        }
        
        // Add rate limit headers
        header('X-RateLimit-Limit: ' . $this->maxRequests);
        header('X-RateLimit-Remaining: ' . $result['remaining']);
        header('X-RateLimit-Reset: ' . $result['reset_time']);
        
        return $result;
    }
    
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    private function cleanOldRecords() {
        $cutoff = time() - ($this->timeWindow * 2); // Keep records for 2x window
        $this->db->query(
            "DELETE FROM rate_limits WHERE window_start < FROM_UNIXTIME(?)",
            [$cutoff]
        );
    }
    
    public function getStats($ipAddress = null) {
        $ipAddress = $ipAddress ?: $this->getClientIP();
        
        return $this->db->fetchAll(
            "SELECT endpoint, request_count, window_start, last_request 
             FROM rate_limits 
             WHERE ip_address = ? 
             ORDER BY last_request DESC",
            [$ipAddress]
        );
    }
}

// Global rate limiter instance
function getRateLimiter() {
    global $db;
    static $rateLimiter = null;
    
    if ($rateLimiter === null) {
        $rateLimiter = new RateLimiter($db);
    }
    
    return $rateLimiter;
}
?>
